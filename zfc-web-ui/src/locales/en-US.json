{"dialogs": {"userDetail": {"title": "User Details", "basicInfo": "Basic Information", "historicalSpeed": "Historical Speed Data", "realtimeSpeed": "Real-time Line Speeds", "trafficUsage": "Traffic Usage", "trafficConsumption": "Daily Traffic Consumption", "uploadSpeed": "Upload Speed", "downloadSpeed": "Download Speed", "totalSpeed": "Total Speed", "clientIPCount": "Client IP Count", "connectionCount": "Connection Count", "clientIPs": "Client IPs", "connections": "Connections", "noData": "No historical data available for the selected time range", "validUntil": "<PERSON>id <PERSON>", "nextReset": "Next Reset", "bandwidth": "Bandwidth", "unlimited": "Unlimited", "portDetails": "Port Forwarding Details", "showPorts": "Show Port Details", "hidePorts": "Hide Port Details", "noPorts": "No port forwarding configured", "failedToLoadPorts": "Failed to load port forwarding data", "lineDetails": "Line Details", "showLines": "Show Line Details", "hideLines": "Hide Line Details", "noLines": "No line data found", "failedToLoadLines": "Failed to load line data"}, "userSpeedDetail": {"title": "My Port Speed Statistics", "historicalData": "My Historical Speed Data", "realtimeSpeed": "Real-time Line Speeds", "uploadSpeed": "Upload Speed", "downloadSpeed": "Download Speed", "totalSpeed": "Total Speed", "trafficConsumption": "Daily Traffic Consumption", "noData": "No historical data for the selected time range", "selectLines": "Select Lines", "selectAll": "Select All", "linePrefix": "Line", "showingLines": "Showing {count} lines with speed data, {total} total lines available", "failedToLoadData": "Failed to load data", "failedToLoadHistorical": "Failed to load historical speed data", "failedToLoadRealtime": "Failed to load real-time speed data", "failedToGetSubscription": "Unable to get user subscription information", "failedToFetchSubscription": "Failed to fetch subscription information", "perInterval": "Per Interval", "trafficInPeriod": "Traffic in period"}, "lineDetails": {"title": "Line Details", "lineName": "Line Name", "entryIp": "Entry IP", "trafficScale": "Traffic Scale", "trafficLimit": "Traffic Limit", "followTotalLimit": "Follow Total Limit", "usedTraffic": "Used Traffic", "portCount": "Port Count", "ports": "ports"}}, "common": {"loading": "Loading...", "processing": "Processing", "logout": "Logout", "confirm": "Confirm", "cancel": "Cancel", "ok": "OK", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "refresh": "Refresh", "close": "Close", "back": "Back", "next": "Next", "submit": "Submit", "reset": "Reset", "language": "Language", "warning": "Warning", "to": "to", "create": "Create", "days": "days", "none": "None", "noData": "No data", "noMatchData": "No matching data", "enabled": "Enabled", "disabled": "Disabled"}, "dashboard": {"title": "Port Forwarding Management", "welcome": "Welcome", "overview": "Overview", "statistics": "Statistics"}, "navigation": {"dashboard": "Dashboard", "servers": "Server Management", "ports": "Port Management", "endpoints": "Forward Endpoints", "subscription": "Subscription", "subscriptionManagement": "Subscription Management", "packageManagement": "Package Management", "licenseRenewal": "License Renewal", "lineDetails": "Line Details", "systemSettings": "System Settings"}, "login": {"title": "<PERSON><PERSON>", "username": "Username", "password": "Password", "loginButton": "<PERSON><PERSON>", "loginFailed": "<PERSON><PERSON> failed", "loginSuccessful": "Login successful", "pleaseEnterUsername": "Please enter username", "pleaseEnterPassword": "Please enter password"}, "servers": {"management": "Server Management", "status": "Status", "online": "Online", "offline": "Offline", "connecting": "Connecting", "name": "Server Name", "address": "Address", "port": "Port", "actions": "Actions", "uptime": "Uptime", "serverOffline": "Server is offline"}, "subscription": {"title": "Subscription", "management": "Subscription Management", "status": "Status", "expiry": "Expiry Date", "renewal": "Renewal", "active": "Active", "expired": "Expired", "license": "License"}, "timeRange": {"1h": "1 Hour", "3h": "3 Hours", "6h": "6 Hours", "24h": "24 Hours", "7d": "7 Days"}, "forms": {"labels": {"id": "ID", "name": "Name", "email": "Email", "tokenId": "Token ID", "serverName": "Server Name", "ipAddress": "IP Address", "interfaceName": "Interface Name", "port": "Port", "portRange": "Port Range", "startPort": "Start Port", "endPort": "End Port", "protocol": "Protocol", "status": "Status", "actions": "Actions", "trafficConsumption": "Traffic Consumption", "trafficScale": "Traffic Scale", "allowForward": "Allow Forward", "allowIpv6": "Allow IPv6", "allowLatencyTest": "Allow Latency Test", "maxIps": "Max IPs", "maxConnectionsPerIp": "Max Connections per IP", "forwardEndpoints": "Forward Endpoints", "balanceStrategy": "Balance Strategy", "protocolFilters": "Protocol Filters", "renewalCode": "Renewal Code", "applicationTime": "Application Time", "codeId": "Code ID", "extendedDays": "Extended Days", "renewalAmount": "<PERSON><PERSON> Amount", "ingressAddress": "Ingress Address", "servePort": "Serve Port", "bindPort": "Bind Port", "entryPoint": "Entry Point", "target": "Target", "targetSelectMode": "Target Select Mode", "testMethod": "Test Method", "line": "Line", "ipDomain": "IP / Domain", "networkTopology": "Network Topology", "priority": "Priority Order", "totServers": "Tot Servers", "totServerSelectMode": "Tot Server Select Mode", "totServerTestMethod": "Tot Server Test Method", "useForwardAsTransport": "Use Forward endpoints as Transport", "displayName": "Display Name", "advancedOptions": "Advanced Options", "priorityOrder": "Priority Order", "portBandwidth": "Port Bandwidth", "customConfig": "Custom Configuration", "tags": "Tags", "acceptProxyProtocol": "Accept Proxy Protocol", "sendProxyProtocol": "Send Proxy Protocol"}, "help": {"servePortHelp": "Public port exposed by the endpoint. Leave empty for auto-assignment.", "bindPortHelp": "Internal port to bind on. For NAT setups where public and internal ports differ, specify both. Leave empty if same as serve port.", "targetSelectMode": "Target select mode determines how to choose among multiple target addresses:<br/>• Best Latency: Select target with lowest latency<br/>• Fallback: Try targets in order, switching to next when previous fails<br/>• Domain Follow: Select target based on domain name<br/>• Round Robin: Use all targets in rotation<br/>• Random: Randomly select targets", "testMethod": "Test method for detecting target address connectivity and latency:<br/>• TCP Ping: Test by establishing TCP connection (recommended)<br/>• ICMP: Test using ICMP protocol ping", "forwardEndpoints": "Forward endpoints are relay servers used for traffic forwarding and load balancing.<br/>• Traffic will be sent to forward endpoints first, then forwarded to the final destination<br/>• Multiple endpoints can be selected for load balancing or redundancy<br/>• Suitable for scenarios requiring access through relay servers", "balanceStrategy": "Balance strategy determines how to distribute traffic among multiple forward endpoints or Tot servers:<br/>• Best Latency: Select server with lowest latency<br/>• Fallback: Switch to backup server when primary fails<br/>• Domain Follow: Select server based on domain name<br/>• Round Robin: Use all servers in rotation<br/>• Random: Randomly select servers", "totServers": "Tot (TCP over multi TCP) servers are used for bandwidth aggregation.<br/>• Traffic enters the server first, then passes through your configured forward endpoints, and finally reaches the Tot server for aggregation<br/>• The number of forward endpoints determines the bandwidth aggregation (e.g., 2 endpoints of 100M each = 200M total)<br/>• Both the Tot server and main server require sufficient bandwidth", "totServerTestMethod": "Tot server test method for detecting Tot server connectivity:<br/>• TCP Ping: Test by establishing TCP connection (recommended)<br/>• ICMP: Test using ICMP protocol ping", "portBandwidth": "Set dedicated bandwidth limit for this port (in Mbps).<br/>• If set, will override subscription-level bandwidth limit<br/>• Leave empty to use subscription bandwidth settings<br/>• Cannot exceed subscription's maximum bandwidth", "trafficScale": "Traffic multiplier affects how traffic consumption is calculated.<br/>• Values less than 1.0 reduce reported usage<br/>• Values greater than 1.0 increase reported usage<br/>• Default is 1.0 (no scaling)<br/>• Example: 0.5 means 1GB actual usage = 0.5GB reported", "allowForward": "Enable this server to use forward endpoints.<br/>• When enabled, server can relay traffic through configured forward endpoints<br/>• Required for bandwidth aggregation and load balancing features<br/>• Disable if server should only handle direct connections", "allowIpv6": "Enable IPv6 protocol support on this server.<br/>• When enabled, server will accept IPv6 connections<br/>• Requires server infrastructure to support IPv6<br/>• Leave disabled if IPv6 is not available or needed", "allowLatencyTest": "Allow latency testing for this server's endpoints.<br/>• When enabled, system can perform latency checks on server connections<br/>• Used for automatic endpoint selection and health monitoring<br/>• Disable to reduce network overhead from testing", "useForwardAsTransport": "Make the server connect to target addresses through selected forward endpoints and Tot servers instead of direct connections.<br/>• When enabled: Traffic path is Server → Forward Endpoint/Tot Server → Target Address<br/>• When disabled: Server connects directly to target addresses<br/>• Suitable for scenarios requiring access through relay servers", "totServerSelectMode": "Selection strategy when multiple Tot servers are configured:<br/>• Best Latency: Choose server with lowest latency<br/>• Fallback: Use servers in priority order<br/>• Domain Follow: Select based on target domain<br/>• Round Robin: Rotate through all servers<br/>• Random: Randomly select servers", "acceptProxyProtocol": "Accept PROXY protocol headers from incoming client connections.<br/>• When enabled, server will parse PROXY protocol v1/v2 headers<br/>• Used to preserve original client IP information through proxies/load balancers<br/>• Only enable if clients send PROXY protocol headers", "sendProxyProtocol": "Send PROXY protocol headers to target servers.<br/>• When enabled, server will prepend PROXY protocol v1 headers when connecting to targets<br/>• Preserves original client IP information for target servers<br/>• Only enable if target servers support PROXY protocol"}, "placeholders": {"enterName": "Enter name", "enterServerName": "Enter server name", "enterIpAddress": "Enter IP address", "enterInterfaceName": "Enter interface name", "enterStartPort": "Start port", "enterEndPort": "End port", "enterRenewalCode": "Enter your renewal code", "enterDisplayName": "Please enter display name", "enterIpOrDomain": "Enter IP address or domain name", "enterServePort": "Enter serve port (1-65535)", "enterBindPort": "Enter bind port (1-65535)", "selectProtocol": "Select protocol", "selectPackage": "Please select a package", "selectForwardEndpoints": "Select forward endpoints", "selectBalanceStrategy": "Select balance strategy", "selectLines": "Select lines", "leaveEmptyUnlimited": "Leave empty for unlimited", "searchById": "Search by exact ID", "searchByName": "Search by name (regex supported)", "searchByAddress": "Search ingress address (regex supported)", "selectTotServers": "Select tot servers", "selectTestMethod": "Select test method", "selectVersions": "Select versions", "selectStatus": "Select status", "searchAndSelectLines": "Search and select lines", "enterBandwidth": "Enter bandwidth limit", "enterCustomConfig": "Enter custom configuration", "selectTags": "Select tags", "enterTags": "Enter or select tags"}, "options": {"bestLatency": "Best Latency", "fallback": "Fallback", "domainFollow": "Domain Follow", "balanceRoundRobin": "Balance (Round Robin)", "balanceRandom": "Balance (Random)", "roundRobin": "Round Robin", "random": "Random", "tcpPing": "TCP Ping", "icmp": "ICMP", "http": "Http", "socks5": "Socks5", "bitTorrent": "BitTorrent", "tls": "Tls", "unknown": "Unknown", "disabled": "Disabled", "proxyProtocolV1": "PROXY Protocol V1", "proxyProtocolV2": "PROXY Protocol V2"}, "validation": {"required": "This field is required", "pleaseEnterName": "Please enter name", "pleaseEnterServerName": "Please enter server name", "pleaseEnterDisplayName": "Please enter display name", "pleaseEnterIpAddress": "Please enter IP address", "pleaseEnterInterfaceName": "Please enter interface name", "pleaseEnterStartPort": "Please enter start port", "pleaseEnterEndPort": "Please enter end port", "pleaseEnterRenewalCode": "Please enter a renewal code", "pleaseInputName": "Please input name", "pleaseInputIngressAddress": "Please input ingress address", "pleaseSelectProtocol": "Please select protocol", "pleaseSelectLine": "Please select line", "pleaseEnterTargetAddress": "Please enter target address", "pleaseSelectTargetSelectMode": "Please select target select mode", "pleaseSelectTestMethod": "Please select test method", "pleaseSelectBalanceStrategy": "Please select a balance strategy", "portMustBeNumber": "Port must be a number", "selectTotTestMethod": "Please select ToT server test method", "selectTotBalanceStrategy": "Please select ToT server balance strategy", "renewalCodeMinLength": "Renewal code must be at least 5 characters", "portRange": "Port must be between 1 and 65535", "failedToTestLatency": "Failed to test latency"}, "advancedOptions": "Advanced Options"}, "actions": {"add": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "ok": "OK", "submit": "Submit", "refresh": "Refresh", "close": "Close", "back": "Back", "next": "Next", "reset": "Reset", "search": "Search", "hideSearch": "Hide Search", "showSearch": "Show Search", "hideSearchFilters": "Hide search filters", "showSearchFilters": "Show search filters", "clear": "Clear", "addServer": "Add Server", "addEndpoint": "Add Forward Endpoint", "addPort": "Add Port Forward", "batchUpdateTargets": "Batch Update Targets", "saveChanges": "Save Changes", "copyInstallScript": "Copy Install Script", "copyEndpoint": "Copy Forward Endpoint", "copyInstallationCommand": "Copy Installation Command", "applyRenewalCode": "Apply Renewal Code", "resume": "Resume", "suspend": "Suspend"}, "status": {"online": "Online", "offline": "Offline", "active": "Active", "expired": "Expired", "expiringSoon": "Expiring Soon"}, "messages": {"confirm": {"deleteServer": "Are you sure you want to delete this server?", "deleteEndpoint": "Are you sure you want to delete this forward endpoint?", "deletePort": "Are you sure you want to delete port \"{name}\" ({address}:{port})?", "deleteSubscription": "This will permanently delete the subscription. Continue?", "resetTraffic": "Are you sure to reset traffic for this subscription? This will clear all traffic usage data.", "suspendPort": "Are you sure you want to suspend port \"{name}\" ({address}:{port})?", "resumePort": "Are you sure you want to resume port \"{name}\" ({address}:{port})?", "warning": "Warning", "suspendPortTitle": "Suspend Port", "resumePortTitle": "Resume Port"}, "success": {"serverAdded": "Server added successfully", "serverDeleted": "Server deleted successfully", "serverModified": "Server modified successfully", "commandCopied": "Installation command copied to clipboard", "endpointCreated": "Forward endpoint created successfully", "endpointUpdated": "Forward endpoint updated successfully", "endpointDeleted": "Forward endpoint deleted successfully", "endpointCopied": "Forward endpoint copied successfully", "portCreated": "Port created successfully", "portDeleted": "Port deleted successfully", "portUpdated": "Port updated successfully", "installScriptCopied": "Install script copied to clipboard", "installCommandCopied": "Installation command copied to clipboard", "licenseRefreshed": "License status refreshed", "renewalApplied": "Renewal code applied successfully!", "subscriptionAdded": "Subscription added successfully", "subscriptionDeleted": "Subscription deleted successfully", "subscriptionUpdated": "Subscription updated successfully", "subscriptionExtended": "Subscription extended successfully", "trafficReset": "Traffic reset successfully", "tokenCopied": "Token ID {tokenId} copied to clipboard for {email} (ID: {subscriptionId})"}, "error": {"failedToFetchEndpoints": "Failed to fetch endpoints", "failedToFetchData": "Failed to fetch data", "failedToCreatePort": "Failed to create port", "failedToCopyInstallScript": "Failed to copy install script", "failedToCopyEndpoint": "Failed to copy forward endpoint", "failedToRefreshLicense": "Failed to refresh license status", "failedToApplyRenewal": "Failed to apply renewal code", "failedToSubmitForm": "Failed to submit form", "failedToDeletePort": "Failed to delete port", "failedToAddServer": "Failed to add server", "failedToDeleteServer": "Failed to delete server", "failedToModifyServer": "Failed to modify server", "failedToCopyCommand": "Failed to copy command to clipboard", "unknownError": "Unknown error", "missingServerData": "Missing server public key or interface name", "invalidServerData": "Unable to edit server: Invalid server data", "missingServerId": "Unable to edit server: Server ID is missing", "failedToLoadServerList": "Failed to load server list", "failedToLoadCompleteServerList": "Failed to load complete server list for search", "failedToAddSubscription": "Failed to add subscription", "failedToUpdateSubscription": "Failed to update subscription", "failedToDeleteSubscription": "Failed to delete subscription", "failedToExtendSubscription": "Failed to extend subscription", "failedToResetTraffic": "Failed to reset traffic", "failedToCopyToken": "Failed to copy token ID to clipboard", "failedToLoadSubscriptions": "Failed to load subscriptions"}, "info": {"noPortsInUse": "No ports in use", "totalItems": "Total {count} items", "itemsPerPage": "{count} / page", "loadingTableData": "Loading table data...", "loadingCardContent": "Loading card content...", "loadingDialogContent": "Loading dialog content...", "loadingPageContent": "Loading page content...", "subscriptionExpiredFeature": "Subscription expired - please renew to use this feature", "subscriptionExpiredWarning": "Your subscription has expired. Please renew your subscription to continue using the service.", "maxUniqueIps": "Maximum number of unique IP addresses that can connect", "maxConnectionsDescription": "Maximum number of connections per IP address", "protocolFilterDescription": "Select protocols to filter/block. Leave empty to allow all protocols.", "customConfigDescription": "Custom configuration allows you to set additional configuration parameters for the server, in JSON or YAML format", "tagsDescription": "Add tags to the server for categorization and easy filtering. You can input new tags or select from existing ones."}}, "pages": {"servers": {"title": "Server Management", "addServerDialog": "Add New Server", "editServerDialog": "Edit Server", "version": "Version", "ports": "Ports", "portDetails": "Port Details", "name": "Name", "ipAddress": "IP Address", "status": "Status", "unknown": "Unknown", "online": "Online", "offline": "Offline", "actions": "Actions", "serverPrefix": "Server", "noPortsInUse": "No ports in use", "portPrefix": "Port", "latencyMonitoring": "Latency Monitoring Management"}, "endpoints": {"title": "Forward Endpoints", "management": "Forward Endpoint Management", "addEndpointDialog": "Add Forward Endpoint", "editEndpointDialog": "Edit Forward Endpoint"}, "ports": {"title": "Port Management", "addPortDialog": "Add Port Forward", "editPortDialog": "Edit Port Forward", "modifyPortDialog": "Modify Port Forward"}, "license": {"title": "License Renewal", "renewalHistory": "Renewal History", "currentLicenseStatus": "Current License Status", "applyRenewalCode": "Apply Renewal Code", "licenseExpires": "License Expires", "daysRemaining": "Days Remaining", "lastRenewal": "Last Renewal", "licenseId": "License ID", "monthlyRate": "Monthly Rate", "maxWorkers": "Max Workers", "maxSubscriptionNumber": "Max Subscription Number", "maxWorkersTooltip": "Maximum number of servers allowed to manage", "maxSubscriptionNumberTooltip": "Maximum number of subscription users allowed", "licenseExpired": "License Expired", "licenseExpiredDescription": "Your software license has expired. Please apply a renewal code to continue using the service.", "licenseExpiringSoon": "License Expiring Soon", "licenseExpiringSoonDescription": "Your license will expire in {days} days. Consider renewing soon to avoid service interruption.", "renewalCodeHint": "Enter the renewal code provided by your software vendor", "confirmRenewal": "Are you sure you want to apply this renewal code? This action cannot be undone.", "confirmRenewalTitle": "Confirm <PERSON>wal", "applyCode": "Apply Code", "refreshStatus": "Refresh Status", "renewalRequests": "Renewal Requests", "submitRenewalRequest": "Submit Renewal Request", "renewalRequestHistory": "Renewal Request History", "noRenewalRequests": "No renewal request records", "requestId": "Request ID", "renewalDuration": "Renewal Duration", "paymentAmount": "Payment Amount", "paymentMethod": "Payment Method", "paymentProof": "Payment Proof", "customerMessage": "Customer Message", "requestStatus": "Request Status", "createdAt": "Created At", "processedAt": "Processed At", "adminNotes": "Admin Notes", "viewDetails": "View Details", "cancelRequest": "Cancel Request", "requestDetail": "Request Details", "alipayHongbao": "Alipay <PERSON>", "cryptocurrency": "Cryptocurrency", "statusPending": "Pending", "statusProcessed": "Processed", "statusCancelled": "Cancelled", "enterPaymentProof": "Please enter payment proof", "enterAlipayHongbao": "Please enter Alipay Hongbao content", "enterCryptocurrencyProof": "Please enter cryptocurrency payment proof", "optionalMessage": "Optional, any additional information you'd like to provide", "priceCalculation": "Price Calculation", "totalPrice": "Total Price", "calculating": "Calculating...", "hasPendingRequest": "There is already a pending renewal request, please cancel it or wait for processing", "renewalRequestCreated": "Renewal request created successfully", "renewalRequestCancelled": "Renewal request cancelled", "confirmCancelRequest": "Are you sure you want to cancel this renewal request?", "confirmCancel": "Confirm Cancel", "failedToCreateRequest": "Failed to create renewal request", "failedToCancelRequest": "Failed to cancel renewal request", "failedToCalculatePrice": "Failed to calculate price", "paymentProofValidation": {"required": "Please enter payment proof", "cryptocurrencyIncomplete": "Cryptocurrency payment proof information incomplete, please provide detailed transaction information"}, "renewalRequestValidation": {"selectDuration": "Please select renewal duration", "selectDurationPlaceholder": "Please select renewal duration", "selectPaymentMethod": "Please select payment method", "enterPaymentProof": "Please enter payment proof"}, "renewalRequestForm": {"title": "Submit Renewal Request", "price": "Price", "months": "months", "monthlyRate": "Monthly Rate", "totalAmount": "Total Amount", "submitRequest": "Submit Request", "close": "Close", "paymentAmountMismatch": "Payment amount does not match calculated price, please recalculate", "formValidationFailed": "Please check if the form is filled correctly", "alipayPlaceholder": "Please enter Alipay red packet command, e.g.: ￥123.45￥abc123", "cryptoPlaceholder": "Please enter cryptocurrency payment proof, including transaction hash, amount, etc.", "alipayHint": "Please ensure the red packet amount matches the displayed price above", "cryptoHint": "Please provide complete transaction information for verification", "optionalMessage": "Optional, any additional notes can be left here"}, "renewalRequestDetail": {"title": "Renewal Request Details", "submissionTime": "Submission Time"}, "conversationHistory": "Conversation History", "customer": "Customer", "admin": "Administrator", "sendMessage": "Send Message", "enterMessage": "Enter your message...", "messageSent": "Message sent successfully", "failedToSendMessage": "Failed to send message", "noMessagesYet": "No messages yet", "notificationMessages": {"permissionRequired": "Renewal request function requires premium subscription privileges. Please contact administrator to upgrade your subscription to use this feature.", "featureNotAvailable": "Feature Not Available", "loadRenewalRequestsFailed": "Failed to load renewal requests"}}, "subscription": {"title": "Subscription Information", "speedDetails": "Speed Details", "validUntil": "<PERSON>id <PERSON>", "nextReset": "Next Reset", "bandwidth": "Bandwidth", "trafficUsage": "Traffic Usage", "unlimited": "Unlimited", "noDataAvailable": "No subscription data available", "serverStatus": "Server Status", "refresh": "Refresh"}, "subscriptionManagement": {"title": "Subscription Management", "addSubscription": "Add Subscription", "hideSearchFilters": "Hide search filters", "showSearchFilters": "Show search filters", "hideSearch": "Hide Search", "showSearch": "Show Search", "searchAndFilterOptions": "Search and filter options", "id": "ID", "tokenId": "Token ID", "email": "Email", "validUntil": "<PERSON>id <PERSON>", "nextReset": "Next Reset", "lines": "Lines", "search": "Search", "clear": "Clear", "searchByExactId": "Search by exact ID", "searchByExactTokenId": "Search by exact Token ID", "searchByEmail": "Search by email (partial match)", "startDate": "Start date", "endDate": "End date", "to": "to", "selectLines": "Select lines", "traffic": "Traffic", "actions": "Actions", "clickToCopy": "Click to copy: ", "lineCount": "Line Count", "maxIps": "Max IPs", "maxConnections": "Max Connections", "forwardEndpoint": "Forward Endpoint", "enabled": "Enabled", "disabled": "Disabled", "view": "View", "edit": "Edit", "extend": "Extend", "resetTraffic": "Reset Traffic", "delete": "Delete", "addUser": "Add User", "editUser": "Edit User", "emailAddress": "Email Address", "bandwidth": "Bandwidth", "trafficGB": "Traffic (GB)", "activateImmediately": "Activate Immediately", "maxPortsPerServer": "Max Ports per Server", "billingType": "Billing Type", "cycleBilling": "Cycle Billing", "onetimeBilling": "One-time Billing", "cycleDays": "Cycle Days", "cyclePrice": "Cycle Price", "onetimePrice": "One-time Price", "totalDays": "Total Days", "allowForwardEndpoint": "Allow Forward Endpoint", "allowIpNum": "Max IPs", "allowConnNum": "Max Connections per IP", "leaveEmptyForUnlimited": "Leave empty for unlimited", "cancel": "Cancel", "add": "Add", "update": "Update", "pleaseEnterEmailAddress": "Please enter email address", "pleaseEnterValidEmailAddress": "Please enter valid email address", "pleaseEnterMaxPortsPerServer": "Please enter max ports per server", "pleaseSelectAtLeastOneLine": "Please select at least one line", "confirmDelete": "Are you sure you want to delete this subscription?", "warning": "Warning", "confirmExtend": "Are you sure you want to extend this subscription by {days} days?", "confirmResetTraffic": "Are you sure you want to reset the traffic for this user?", "userAdded": "User added successfully", "userUpdated": "User updated successfully", "userDeleted": "User deleted successfully", "subscriptionExtended": "Subscription extended successfully", "trafficReset": "Traffic reset successfully", "failedToAddUser": "Failed to add user", "failedToUpdateUser": "Failed to update user", "failedToDeleteUser": "Failed to delete user", "failedToExtendSubscription": "Failed to extend subscription", "failedToResetTraffic": "Failed to reset traffic", "failedToLoadSubscriptions": "Failed to load subscriptions", "failedToLoadServers": "Failed to load servers", "tokenIdCopied": "Token ID copied to clipboard", "failedToCopyTokenId": "Failed to copy token ID", "editSubscription": "Edit Subscription", "cycle": "Cycle", "onetime": "One Time", "cycleDaysLabel": "Cycle Days", "cyclePriceLabel": "Cycle Price", "totalDaysLabel": "Total Days", "onetimePriceLabel": "One Time Price", "trafficResetCycleLabel": "Traffic Reset Cycle", "trafficResetCyclePlaceholder": "Enter traffic reset days", "trafficResetCycleDescription": "Days interval for traffic reset, independent of subscription validity period", "linesLabel": "Lines", "searchAndSelectLines": "Search and select lines", "maxIpsLabel": "Max IPs", "maxConnectionsPerIpLabel": "Max Connections per IP", "activateImmediatelyLabel": "Activate Immediately", "allowForwardEndpointLabel": "Allow Forward Endpoint", "enterTrafficLimitGB": "Enter traffic limit in GB", "enterEmailAddress": "Enter email address", "packageLines": "Package Lines", "additionalLines": "Additional Lines", "includedInPackage": "Included in Package", "selectAdditionalLines": "Select additional lines", "multiplier": "Multiplier", "trafficMultiplier": "Traffic Multiplier", "cycleBillingLabel": "Cycle Billing", "onetimeBillingLabel": "One-time Billing", "usingPackageConfig": "Using Package Configuration", "packageConfigDescription": "Package selected. Billing and line configuration will be automatically obtained from the package.", "fieldManagedByPackage": "This field is managed by the package. Please edit the package configuration to modify it.", "editPackageConfig": "Edit Package Configuration"}, "packageManagement": {"title": "Package Management", "createPackage": "Create Package", "editPackage": "Edit Package", "packageName": "Package Name", "displayName": "Display Name", "description": "Description", "totalTraffic": "Total Traffic", "bandwidth": "Bandwidth Limit", "maxPortsPerServer": "Max Ports per Server", "allowForwardEndpoint": "Allow Forward Endpoint", "allowIpNum": "Allow IP Number", "allowConnNum": "Connections per IP Limit", "isActive": "Package Status", "isDefault": "Default Package", "packageLines": "Package Lines", "lineCount": "Line Count", "status": "Status", "active": "Active", "inactive": "Inactive", "default": "<PERSON><PERSON><PERSON>", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "cancel": "Cancel", "save": "Save", "selectLine": "Select Line", "bandwidthLimit": "Bandwidth Limit (Mbps)", "trafficScale": "Traffic Scale", "lineTraffic": "Line Traffic (GB)", "addLine": "Add Line", "removeLine": "Remove Line", "pleaseEnterPackageName": "Please enter package name", "pleaseEnterDisplayName": "Please enter display name", "pleaseSelectAtLeastOneLine": "Please select at least one line", "packageNameExists": "Package name already exists", "trafficGB": "Traffic (GB)", "leaveEmptyForUnlimited": "Leave empty for unlimited", "leaveEmptyForUnlimitedPackage": "Leave empty to not use package restrictions, system default configuration will be used", "enterTrafficLimitGB": "Enter traffic limit in GB", "maxIpsLabel": "Max IPs", "maxConnectionsPerIpLabel": "Max Connections per IP", "maxIpsDescription": "Maximum number of unique IP addresses that can connect", "maxConnectionsPerIpDescription": "Maximum number of connections per IP address", "leaveEmptyUnlimited": "Leave empty for unlimited", "packageCreatedSuccessfully": "Package created successfully", "packageUpdatedSuccessfully": "Package updated successfully", "packageDeletedSuccessfully": "Package deleted successfully", "requiredFields": "Required <PERSON>", "optionalFields": "Optional Configuration", "confirmDeletePackage": "Are you sure you want to delete this package?", "cannotDeletePackageInUse": "Cannot delete package that is in use", "failedToCreatePackage": "Failed to create package", "failedToUpdatePackage": "Failed to update package", "failedToDeletePackage": "Failed to delete package", "failedToLoadPackages": "Failed to load package list", "searchByName": "Search by name", "filterByStatus": "Filter by status", "allStatuses": "All Statuses", "onlyActive": "Only Active", "onlyInactive": "Only Inactive", "onlyDefault": "Only De<PERSON>ult", "allLinesSelected": "All lines are selected", "trafficScaleWarning": "When traffic scale is not 1, traffic limit should be set to the scaled value", "search": "Search", "clear": "Clear", "selectPackage": "Select Package"}}, "latencyTest": {"title": "Latency Test", "testing": "Testing", "result": "Latency Test Result", "error": "Latency Test Error"}, "connectionStats": {"title": "Connection Statistics", "loading": "Loading", "error": "Connection Statistics Error", "errorMessage": "Failed to get connection statistics", "noForwarders": "Direct", "columns": {"id": "Connection ID", "startTime": "Start Time", "clientAddr": "Client Address", "targetAddr": "Target Address", "connType": "Type", "forwarders": "Forwarders"}}, "theme": {"light": "Light Theme", "dark": "Dark Theme", "auto": "Auto Theme"}, "latencyMonitoring": {"title": "Latency Monitoring", "addConfig": "Add Monitoring Config", "realTimeStatus": "Real-time Latency Status", "historyData": "Historical Latency Data", "configManagement": "Monitoring Configuration", "refresh": "Refresh", "noConfigs": "No monitoring configurations", "testSuccess": "Test Success", "testFailed": "Test Failed", "lastTest": "Last Test", "timeRanges": {"1h": "1 Hour", "3h": "3 Hours", "6h": "6 Hours", "12h": "12 Hours", "24h": "24 Hours", "7d": "7 Days"}, "configDialog": {"addTitle": "Add Monitoring Configuration", "editTitle": "Edit Monitoring Configuration", "displayName": "Configuration Name", "targetAddress": "Target Address", "testType": "Test Type", "targetPort": "Target Port", "packetSize": "Packet Size", "timeout": "Timeout", "interval": "Test Interval", "alertThreshold": "<PERSON><PERSON>", "isPublic": "Visible to All Users", "isEnabled": "Enable Monitoring", "hints": {"targetAddress": "Supports IP addresses and domain names", "tcpPort": "TCP tests require specifying a port", "packetSize": "ICMP packet size, default 64 bytes", "timeout": "Test timeout, default 5000 milliseconds", "interval": "Latency test interval, default 60 seconds", "alertThreshold": "<PERSON><PERSON> after consecutive failures, default 3 times", "isPublic": "When checked, regular users can also view this monitoring configuration data", "isEnabled": "When disabled, monitoring for this target will be stopped"}, "validation": {"displayNameRequired": "Please enter configuration name", "displayNameLength": "Name length should be 2-50 characters", "targetAddressRequired": "Please enter target address", "targetAddressInvalid": "Please enter a valid IP address or domain name", "testTypeRequired": "Please select test type", "tcpPortRequired": "TCP test requires a valid port number (1-65535)", "packetSizeRange": "Packet size range is 8-1500 bytes", "timeoutRange": "Timeout range is 1000-30000 milliseconds", "intervalRange": "Test interval range is 10-3600 seconds", "alertThresholdRange": "Alert threshold range is 1-10 times"}}, "table": {"name": "Name", "targetAddress": "Target Address", "testType": "Test Type", "interval": "Interval (s)", "status": "Status", "actions": "Actions", "edit": "Edit", "delete": "Delete"}, "messages": {"configCreated": "Configuration created successfully", "configUpdated": "Configuration updated successfully", "configDeleted": "Deleted successfully", "statusUpdated": "Configuration status updated successfully", "loadRealtimeDataFailed": "Failed to load real-time data", "loadHistoryDataFailed": "Failed to load historical data", "loadConfigsFailed": "Failed to load configurations", "updateStatusFailed": "Failed to update configuration status", "deleteFailed": "Delete failed", "createFailed": "Create failed", "updateFailed": "Update failed", "confirmDelete": "Are you sure you want to delete monitoring configuration \"{name}\"?"}}, "batchUpdate": {"title": "Batch Update Port Targets", "simpleMode": "Easy Mode", "advancedMode": "Advanced Mode", "simpleModeDesc": "Simple and intuitive operations, no need to understand technical syntax", "advancedModeDesc": "Use regular expressions for complex pattern matching and replacement", "replaceType": "Replace Type", "selectReplaceType": "Please select a replace type", "regexPattern": "Regex Pattern", "replacement": "Replacement", "matchRule": "Match Rule", "newFormat": "New Format", "regexPlaceholder": "Enter regex pattern, e.g., 192\\.168\\.1\\.(\\d+)", "replacementPlaceholder": "Enter replacement text, e.g., 10.0.0.$1", "regexHelp": "Use regex to match target addresses that need to be updated", "replacementHelp": "Use $1, $2, etc. to reference captured groups from the regex", "types": {"ipAddress": "IP Address Replace", "ipAddressDesc": "Replace specific IP addresses while keeping port numbers unchanged", "port": "Port Replace", "portDesc": "Replace specific port numbers while keeping IP addresses unchanged", "exact": "Exact Address Replace", "exactDesc": "Replace complete addresses exactly (IP:Port format)", "batchAdd": "<PERSON><PERSON> Add", "batchAddDesc": "Add new target addresses to existing ports", "batchRemove": "Batch Remove", "batchRemoveDesc": "Remove matching target addresses from existing ports"}, "originalIpAddress": "Original IP Address", "newIpAddress": "New IP Address", "originalIpAddressPlaceholder": "e.g., *************", "newIpAddressPlaceholder": "e.g., *********", "originalIpAddressHelp": "Enter the complete IP address to be replaced", "newIpAddressHelp": "Enter the new IP address, port numbers will be preserved", "originalPort": "Original Port", "newPort": "New Port", "originalPortPlaceholder": "e.g., 8080", "newPortPlaceholder": "e.g., 9080", "originalPortHelp": "Enter the port number to be replaced", "newPortHelp": "Enter the new port number, IP addresses will be preserved", "originalAddress": "Original Address", "newAddress": "New Address", "originalAddressPlaceholder": "e.g., *************:8080", "newAddressPlaceholder": "e.g., **********:9080", "originalAddressHelp": "Enter the complete address to be replaced (IP:Port format)", "newAddressHelp": "Enter the new complete address (IP:Port format)", "batchAddAddress": "Add Address", "removeAddress": "Remove Address", "batchAddAddressPlaceholder": "e.g., *************:8080", "removeAddressPlaceholder": "e.g., *************:8080", "batchAddAddressHelp": "Enter the complete address to add (IP:Port format)", "removeAddressHelp": "Enter the complete address to remove (IP:Port format)", "conditionAddress": "Condition Address", "conditionAddressPlaceholder": "e.g., *************:8080 (leave empty to add to all ports)", "conditionAddressHelp": "When specified, only ports containing this address will have the new address added; leave empty to add to all ports", "example": "Example", "preview": "Preview Changes", "execute": "Execute Update", "previewResults": "Preview Results", "executionResults": "Execution Results", "affectedPorts": "Found {count} matching ports", "portName": "Port Name", "changes": "Changes", "status": "Status", "willUpdate": "Will Update", "noChange": "No Change", "successCount": "Successfully updated: {count}", "failedCount": "Failed: {count}", "updatedPortsDetails": "Updated Port Details", "closeAndRefresh": "Close & Refresh", "noMatchingPorts": "No ports match the specified pattern", "noChangesNeeded": "No changes would be made with this pattern", "previewComplete": "Preview complete, {count} ports will be updated", "executeSuccess": "Successfully updated {count} ports", "noPortsUpdated": "No ports were updated", "executePartialFailure": "Partially successful: {success} updated, {failed} failed", "previewFailed": "Failed to preview changes", "executeFailed": "Failed to execute update", "validation": {"regexRequired": "Please enter a regex pattern", "replacementRequired": "Please enter replacement text", "invalidRegex": "Invalid regex pattern", "replaceTypeRequired": "Please select a replace type", "originalIpAddressRequired": "Please enter original IP address", "newIpAddressRequired": "Please enter new IP address", "invalidIpAddressFormat": "Invalid IP address format, should be like *************", "originalPortRequired": "Please enter original port", "newPortRequired": "Please enter new port", "invalidPortFormat": "Invalid port format, should be numbers only", "originalAddressRequired": "Please enter original address", "newAddressRequired": "Please enter new address", "invalidAddressFormat": "Invalid address format, should be like *************:8080", "batchAddAddressRequired": "Please enter address to add", "removeAddressRequired": "Please enter address to remove", "cannotRemoveAllTargets": "Cannot remove all target addresses - at least one address must remain"}}, "brandName": "Firefly World", "search": {"filterOptionsLabel": "Search and filter options"}, "systemSettings": {"title": "System Settings", "description": "Manage global system configurations and user permissions", "sections": {"website": "Website Configuration", "userPermissions": "User Permissions"}, "siteTitle": {"label": "Website Title", "description": "Set the website title for different language environments", "zh": "Chinese", "en": "English", "zhPlaceholder": "Enter Chinese website title", "enPlaceholder": "Enter English website title"}, "allowLatencyMonitoring": {"label": "Allow Users to Add Latency Monitoring", "description": "Control whether regular users can add latency monitoring configurations for servers"}}}