{"dialogs": {"userDetail": {"title": "用户详情", "basicInfo": "基本信息", "historicalSpeed": "历史速度数据", "realtimeSpeed": "实时线路速度", "trafficUsage": "流量使用情况", "trafficConsumption": "每日流量消耗", "uploadSpeed": "上传速度", "downloadSpeed": "下载速度", "totalSpeed": "总速度", "clientIPCount": "客户端IP数量", "connectionCount": "连接数量", "clientIPs": "客户端IP", "connections": "连接数", "noData": "所选时间段内没有历史数据", "validUntil": "有效期至", "nextReset": "下次重置", "bandwidth": "带宽", "unlimited": "无限制", "portDetails": "端口转发详情", "showPorts": "显示端口详情", "hidePorts": "隐藏端口详情", "noPorts": "未配置端口转发", "failedToLoadPorts": "加载端口转发数据失败", "lineDetails": "线路详细信息", "showLines": "显示线路详情", "hideLines": "隐藏线路详情", "noLines": "未找到线路数据", "failedToLoadLines": "加载线路数据失败"}, "userSpeedDetail": {"title": "我的端口速度统计", "historicalData": "我的历史速度数据", "realtimeSpeed": "实时线路速度", "uploadSpeed": "上传速度", "downloadSpeed": "下载速度", "totalSpeed": "总速度", "trafficConsumption": "每日流量消耗", "noData": "所选时间段内没有历史数据", "selectLines": "选择线路", "selectAll": "全选", "linePrefix": "线路", "showingLines": "显示了 {count} 条有速度的线路，共有 {total} 条线路可用", "failedToLoadData": "加载数据失败", "failedToLoadHistorical": "加载历史速度数据失败", "failedToLoadRealtime": "加载实时速度数据失败", "failedToGetSubscription": "无法获取用户订阅信息", "failedToFetchSubscription": "获取订阅信息失败", "perInterval": "按时间段", "trafficInPeriod": "时间段内流量"}, "lineDetails": {"title": "线路详细信息", "lineName": "线路名称", "entryIp": "入口IP", "trafficScale": "流量倍率", "trafficLimit": "流量限制", "followTotalLimit": "按照总流量限制", "usedTraffic": "已用流量", "portCount": "端口数量", "ports": "个端口"}}, "common": {"loading": "加载中...", "processing": "处理中", "logout": "退出登录", "confirm": "确认", "cancel": "取消", "ok": "确定", "save": "保存", "delete": "删除", "edit": "编辑", "add": "添加", "search": "搜索", "refresh": "刷新", "close": "关闭", "back": "返回", "next": "下一步", "submit": "提交", "reset": "重置", "language": "语言", "warning": "警告", "to": "到", "create": "创建", "days": "天", "none": "无", "noData": "暂无数据", "noMatchData": "无匹配数据", "enabled": "启用", "disabled": "禁用"}, "dashboard": {"title": "端口转发管理", "welcome": "欢迎", "overview": "概览", "statistics": "统计信息"}, "navigation": {"dashboard": "仪表板", "servers": "服务器管理", "ports": "端口管理", "endpoints": "转发端点", "subscription": "订阅", "subscriptionManagement": "订阅管理", "packageManagement": "套餐管理", "licenseRenewal": "许可证续费", "lineDetails": "线路详细", "systemSettings": "系统设置"}, "login": {"title": "登录", "username": "用户名", "password": "密码", "loginButton": "登录", "loginFailed": "登录失败", "loginSuccessful": "登录成功", "pleaseEnterUsername": "请输入用户名", "pleaseEnterPassword": "请输入密码"}, "servers": {"management": "服务器管理", "status": "状态", "online": "在线", "offline": "离线", "connecting": "连接中", "name": "服务器名称", "address": "地址", "port": "端口", "actions": "操作", "uptime": "在线时间", "serverOffline": "服务器离线"}, "subscription": {"title": "订阅", "management": "订阅管理", "status": "状态", "expiry": "到期日期", "renewal": "续费", "active": "有效", "expired": "已过期", "license": "许可证"}, "timeRange": {"1h": "1 小时", "3h": "3 小时", "6h": "6 小时", "24h": "24 小时", "7d": "7 天"}, "forms": {"labels": {"id": "ID", "name": "名称", "email": "邮箱", "tokenId": "令牌ID", "serverName": "服务器名称", "ipAddress": "IP地址", "interfaceName": "网卡名称", "port": "端口", "portRange": "端口范围", "startPort": "起始端口", "endPort": "结束端口", "protocol": "协议", "status": "状态", "actions": "操作", "trafficConsumption": "流量消耗", "trafficScale": "流量倍率", "allowForward": "允许自带转发端点", "allowIpv6": "允许IPv6", "allowLatencyTest": "允许延迟测试", "maxIps": "最大IP数", "maxConnectionsPerIp": "每IP最大连接数", "forwardEndpoints": "转发端点", "balanceStrategy": "均衡策略", "protocolFilters": "协议过滤器", "renewalCode": "续费代码", "applicationTime": "申请时间", "codeId": "代码ID", "extendedDays": "延长天数", "renewalAmount": "续费金额", "ingressAddress": "入口地址", "servePort": "服务端口", "bindPort": "绑定端口", "entryPoint": "入口点", "target": "目标", "targetSelectMode": "目标选择模式", "testMethod": "测试方法", "line": "线路", "ipDomain": "IP / 域名", "networkTopology": "网络拓扑", "priority": "优先级顺序", "totServers": "Tot 服务器", "totServerSelectMode": "Tot 服务器选择模式", "totServerTestMethod": "Tot 服务器测试方法", "useForwardAsTransport": "使用转发端点作为传输", "displayName": "显示名称", "advancedOptions": "高级选项", "priorityOrder": "优先级顺序", "portBandwidth": "端口带宽", "customConfig": "自定义配置", "tags": "标签", "acceptProxyProtocol": "接受代理协议", "sendProxyProtocol": "发送代理协议"}, "help": {"servePortHelp": "端点对外暴露的公网端口。留空表示自动分配。", "bindPortHelp": "内网绑定端口。如果是NAT环境且公网端口与内网端口不同，需要明确指定两个端口。如与服务端口相同则留空。", "targetSelectMode": "目标选择模式决定如何在多个目标地址中进行选择：<br/>• 最佳延迟：选择延迟最低的目标<br/>• 备用：按顺序尝试，前一个失败时切换到下一个<br/>• 域名跟随：根据域名选择相应的目标<br/>• 均衡(轮询)：轮流使用所有目标<br/>• 均衡(随机)：随机选择目标", "testMethod": "测试方法用于检测目标地址的连通性和延迟：<br/>• TCP Ping：通过建立TCP连接测试（推荐）<br/>• ICMP：使用ICMP协议ping测试", "forwardEndpoints": "转发端点是中转服务器，用于流量中继和负载均衡。<br/>• 流量将先发送到转发端点，再由转发端点转发到最终目标<br/>• 可以选择多个端点进行负载均衡或冗余<br/>• 适用于需要通过中转服务器访问目标的场景", "balanceStrategy": "均衡策略决定如何在多个转发端点或Tot服务器之间分配流量：<br/>• 最佳延迟：选择延迟最低的服务器<br/>• 备用：主服务器故障时切换到备用服务器<br/>• 域名跟随：根据域名选择服务器<br/>• 轮询：轮流使用所有服务器<br/>• 随机：随机选择服务器", "totServers": "Tot（TCP over multi TCP）服务器用于带宽聚合。<br/>• 流量先进入服务器，再通过你配置的多个转发端点，最终到Tot服务器进行聚合<br/>• 转发端点数量决定了带宽聚合效果（例如：2个100M端点 = 200M总带宽）<br/>• 要求Tot服务器和主服务器都有足够大的带宽", "totServerTestMethod": "Tot服务器测试方法用于检测Tot服务器的连通性：<br/>• TCP Ping：通过建立TCP连接测试（推荐）<br/>• ICMP：使用ICMP协议ping测试", "portBandwidth": "为该端口设置专用的带宽限制（单位：Mbps）。<br/>• 如果设置，将覆盖订阅级别的带宽限制<br/>• 留空表示使用订阅的带宽设置<br/>• 不能超过订阅配置的最大带宽", "trafficScale": "流量倍率影响流量消耗的计算方式。<br/>• 小于1.0的值会减少报告的使用量<br/>• 大于1.0的值会增加报告的使用量<br/>• 默认值是1.0（无缩放）<br/>• 例如：0.5表示实际使用1GB = 报告使用0.5GB", "allowForward": "启用此服务器使用转发端点功能。<br/>• 启用后，服务器可以通过配置的转发端点中转流量<br/>• 带宽聚合和负载均衡功能需要此选项<br/>• 如果服务器只处理直连，可禁用此选项", "allowIpv6": "在此服务器上启用IPv6协议支持。<br/>• 启用后，服务器将接受IPv6连接<br/>• 需要服务器基础设施支持IPv6<br/>• 如果IPv6不可用或不需要，请保持禁用", "allowLatencyTest": "允许对此服务器的端点进行延迟测试。<br/>• 启用后，系统可以对服务器连接执行延迟检查<br/>• 用于自动端点选择和健康监控<br/>• 禁用可减少测试产生的网络开销", "useForwardAsTransport": "让服务器通过选择的转发端点和Tot服务器连接到目标地址，而非直接连接。<br/>• 启用后：流量路径为 服务器 → 转发端点/Tot服务器 → 目标地址<br/>• 禁用后：服务器直接连接到目标地址<br/>• 适用于需要通过中转服务器访问目标的场景", "totServerSelectMode": "配置多个Tot服务器时的选择策略：<br/>• 最佳延迟：选择延迟最低的服务器<br/>• 备用：按优先级顺序使用服务器<br/>• 域名跟随：根据目标域名选择<br/>• 轮询：轮流使用所有服务器<br/>• 随机：随机选择服务器", "acceptProxyProtocol": "接受来自客户端连接的PROXY协议标头。<br/>• 启用时，服务器将解析PROXY协议v1/v2标头<br/>• 用于通过代理/负载均衡器保留原始客户端IP信息<br/>• 仅在客户端发送PROXY协议标头时启用", "sendProxyProtocol": "向目标服务器发送PROXY协议标头。<br/>• 启用时，服务器在连接目标时将添加PROXY协议v1标头<br/>• 为目标服务器保留原始客户端IP信息<br/>• 仅在目标服务器支持PROXY协议时启用", "interfaceName": "网卡名称用于网络速度监控显示。<br/>• 提示：在服务器执行 'ip a' 命令获取正确的网卡名称<br/>• 不影响功能使用，只影响网络速度监控的显示<br/>• 默认值为 'eth0'，适用于大多数服务器"}, "placeholders": {"enterName": "请输入名称", "enterServerName": "请输入服务器名称", "enterIpAddress": "请输入IP地址", "enterInterfaceName": "网卡名称 (默认: eth0)", "enterStartPort": "起始端口", "enterEndPort": "结束端口", "enterRenewalCode": "请输入续费代码", "enterDisplayName": "请输入显示名称", "enterIpOrDomain": "请输入IP地址或域名", "enterServePort": "请输入服务端口 (1-65535)", "enterBindPort": "请输入绑定端口 (1-65535)", "selectProtocol": "选择协议", "selectPackage": "请选择套餐", "selectForwardEndpoints": "选择转发端点", "selectBalanceStrategy": "选择均衡策略", "selectLines": "选择线路", "leaveEmptyUnlimited": "留空表示无限制", "searchById": "按确切ID搜索", "searchByName": "按名称搜索 (支持正则)", "searchByAddress": "搜索入口地址 (支持正则)", "selectTotServers": "选择 Tot 服务器", "selectTestMethod": "选择测试方法", "selectVersions": "选择版本", "selectStatus": "选择状态", "searchAndSelectLines": "搜索并选择线路", "enterBandwidth": "请输入带宽限制", "enterCustomConfig": "请输入自定义配置", "selectTags": "选择标签", "enterTags": "输入或选择标签"}, "options": {"bestLatency": "最佳延迟", "fallback": "备用", "domainFollow": "域名跟随", "balanceRoundRobin": "均衡 (轮询)", "balanceRandom": "均衡 (随机)", "roundRobin": "轮询", "random": "随机", "tcpPing": "TCP Ping", "icmp": "ICMP", "http": "Http", "socks5": "Socks5", "bitTorrent": "BitTorrent", "tls": "Tls", "unknown": "未知", "disabled": "关闭", "proxyProtocolV1": "PROXY协议 V1", "proxyProtocolV2": "PROXY协议 V2"}, "validation": {"required": "此字段为必填项", "pleaseEnterName": "请输入名称", "pleaseEnterServerName": "请输入服务器名称", "pleaseEnterDisplayName": "请输入显示名称", "pleaseEnterIpAddress": "请输入IP地址", "pleaseEnterInterfaceName": "请输入网卡名称", "pleaseEnterStartPort": "请输入起始端口", "pleaseEnterEndPort": "请输入结束端口", "pleaseEnterRenewalCode": "请输入续费代码", "pleaseInputName": "请输入名称", "pleaseInputIngressAddress": "请输入入口地址", "pleaseSelectProtocol": "请选择协议", "pleaseSelectLine": "请选择线路", "pleaseEnterTargetAddress": "请输入目标地址", "pleaseSelectTargetSelectMode": "请选择目标选择模式", "pleaseSelectTestMethod": "请选择测试方法", "pleaseSelectBalanceStrategy": "请选择负载均衡策略", "portMustBeNumber": "端口必须是数字", "selectTotTestMethod": "请选择 ToT 服务器测试方法", "selectTotBalanceStrategy": "请选择 ToT 服务器负载均衡策略", "renewalCodeMinLength": "续费代码至少需要5个字符", "portRange": "端口必须在1到65535之间", "failedToTestLatency": "延迟测试失败"}, "advancedOptions": "高级选项"}, "actions": {"add": "添加", "edit": "编辑", "delete": "删除", "save": "保存", "cancel": "取消", "confirm": "确认", "ok": "确定", "submit": "提交", "refresh": "刷新", "close": "关闭", "back": "返回", "next": "下一步", "reset": "重置", "search": "搜索", "hideSearch": "隐藏搜索", "showSearch": "显示搜索", "hideSearchFilters": "隐藏搜索过滤器", "showSearchFilters": "显示搜索过滤器", "clear": "清除", "addServer": "添加服务器", "addEndpoint": "添加转发端点", "addPort": "添加端口转发", "batchUpdateTargets": "批量更新目标", "saveChanges": "保存更改", "copyInstallScript": "复制安装脚本", "copyInstallationCommand": "复制安装命令", "copyEndpoint": "复制转发端点", "applyRenewalCode": "应用续费代码", "resume": "恢复", "suspend": "暂停"}, "status": {"online": "在线", "offline": "离线", "active": "活跃", "expired": "已过期", "expiringSoon": "即将过期"}, "messages": {"confirm": {"deleteServer": "确定要删除此服务器吗？", "deleteEndpoint": "确定要删除此转发端点吗？", "deletePort": "确定要删除端口 \"{name}\" ({address}:{port}) 吗？", "deleteSubscription": "这将永久删除订阅。是否继续？", "resetTraffic": "确定要重置此订阅的流量吗？这将清除所有流量使用数据。", "suspendPort": "确定要暂停端口 \"{name}\" ({address}:{port}) 吗？", "resumePort": "确定要恢复端口 \"{name}\" ({address}:{port}) 吗？", "warning": "警告", "suspendPortTitle": "暂停端口", "resumePortTitle": "恢复端口"}, "success": {"serverAdded": "服务器添加成功", "serverDeleted": "服务器删除成功", "serverModified": "服务器修改成功", "commandCopied": "安装命令已复制到剪贴板", "endpointCreated": "转发端点创建成功", "endpointUpdated": "转发端点更新成功", "endpointDeleted": "转发端点删除成功", "endpointCopied": "转发端点复制成功", "portCreated": "端口创建成功", "portDeleted": "端口删除成功", "portUpdated": "端口更新成功", "installScriptCopied": "安装脚本已复制到剪贴板", "installCommandCopied": "安装命令已复制到剪贴板", "licenseRefreshed": "许可证状态已刷新", "renewalApplied": "续费代码应用成功！", "subscriptionAdded": "订阅添加成功", "subscriptionDeleted": "订阅删除成功", "subscriptionUpdated": "订阅更新成功", "subscriptionExtended": "订阅延期成功", "trafficReset": "流量重置成功", "tokenCopied": "令牌ID {tokenId} 已为 {email} (ID: {subscriptionId}) 复制到剪贴板"}, "error": {"failedToFetchEndpoints": "获取端点失败", "failedToFetchData": "获取数据失败", "failedToCreatePort": "创建端口失败", "failedToCopyInstallScript": "复制安装脚本失败", "failedToCopyEndpoint": "复制转发端点失败", "failedToRefreshLicense": "刷新许可证状态失败", "failedToApplyRenewal": "应用续费代码失败", "failedToSubmitForm": "提交表单失败", "failedToDeletePort": "删除端口失败", "failedToAddServer": "添加服务器失败", "failedToDeleteServer": "删除服务器失败", "failedToModifyServer": "修改服务器失败", "failedToCopyCommand": "复制命令到剪贴板失败", "unknownError": "未知错误", "missingServerData": "缺少服务器公钥或网卡名称", "invalidServerData": "无法编辑服务器：服务器数据无效", "missingServerId": "无法编辑服务器：服务器ID缺失", "failedToLoadServerList": "加载服务器列表失败", "failedToLoadCompleteServerList": "加载完整服务器搜索列表失败", "failedToAddSubscription": "添加订阅失败", "failedToUpdateSubscription": "更新订阅失败", "failedToDeleteSubscription": "删除订阅失败", "failedToExtendSubscription": "延期订阅失败", "failedToResetTraffic": "重置流量失败", "failedToCopyToken": "复制令牌ID失败", "failedToLoadSubscriptions": "加载订阅失败"}, "info": {"noPortsInUse": "没有在使用的端口", "totalItems": "共 {count} 项", "itemsPerPage": "{count} 条/页", "loadingTableData": "正在加载表格数据...", "loadingCardContent": "正在加载卡片内容...", "loadingDialogContent": "正在加载对话框内容...", "loadingPageContent": "正在加载页面内容...", "subscriptionExpiredFeature": "订阅已过期 - 请续费以使用此功能", "subscriptionExpiredWarning": "您的订阅已过期。请续费以继续使用服务。", "maxUniqueIps": "可连接的唯一IP地址的最大数量", "maxConnectionsDescription": "每个IP地址的最大连接数", "protocolFilterDescription": "选择要过滤/阻止的协议。留空表示允许所有协议。", "customConfigDescription": "自定义配置允许您为服务器设置额外的配置参数，格式为JSON或YAML", "tagsDescription": "为服务器添加标签以便分类和过滤查找。支持输入新标签或选择已有标签。"}}, "pages": {"servers": {"title": "服务器管理", "addServerDialog": "添加新服务器", "editServerDialog": "编辑服务器", "version": "版本", "ports": "端口", "portDetails": "端口详情", "name": "名称", "ipAddress": "IP地址", "status": "状态", "unknown": "未知", "online": "在线", "offline": "离线", "actions": "操作", "serverPrefix": "服务器", "noPortsInUse": "没有在使用的端口", "portPrefix": "端口", "latencyMonitoring": "延迟监控管理"}, "endpoints": {"title": "转发端点", "management": "转发端点管理", "addEndpointDialog": "添加转发端点", "editEndpointDialog": "编辑转发端点"}, "ports": {"title": "端口管理", "addPortDialog": "添加端口转发", "editPortDialog": "编辑端口转发", "modifyPortDialog": "修改端口转发"}, "license": {"title": "许可证续费", "renewalHistory": "续费历史", "currentLicenseStatus": "当前许可证状态", "applyRenewalCode": "应用续费代码", "licenseExpires": "许可证到期时间", "daysRemaining": "剩余天数", "lastRenewal": "最后续费时间", "licenseId": "许可证ID", "monthlyRate": "月费", "maxWorkers": "最大服务器数", "maxSubscriptionNumber": "最大订阅用户数", "maxWorkersTooltip": "最大允许管理多少个服务器", "maxSubscriptionNumberTooltip": "最大允许多少个订阅用户", "licenseExpired": "许可证已过期", "licenseExpiredDescription": "您的软件许可证已过期。请应用续费代码以继续使用服务。", "licenseExpiringSoon": "许可证即将过期", "licenseExpiringSoonDescription": "您的许可证将在 {days} 天内过期。建议尽快续费以避免服务中断。", "renewalCodeHint": "输入软件供应商提供的续费代码", "confirmRenewal": "您确定要应用此续费代码吗？此操作无法撤消。", "confirmRenewalTitle": "确认续费", "applyCode": "应用代码", "refreshStatus": "刷新状态", "renewalRequests": "续费请求", "submitRenewalRequest": "提交续费请求", "renewalRequestHistory": "续费请求历史", "noRenewalRequests": "暂无续费请求记录", "requestId": "请求ID", "renewalDuration": "续费时长", "paymentAmount": "付款金额", "paymentMethod": "支付方式", "paymentProof": "支付凭证", "customerMessage": "客户留言", "requestStatus": "请求状态", "createdAt": "创建时间", "processedAt": "处理时间", "adminNotes": "管理员备注", "viewDetails": "查看详情", "cancelRequest": "取消请求", "requestDetail": "请求详情", "alipayHongbao": "支付宝口令红包", "cryptocurrency": "加密货币", "statusPending": "待处理", "statusProcessed": "已处理", "statusCancelled": "已取消", "enterPaymentProof": "请输入支付凭证", "enterAlipayHongbao": "请输入支付宝口令红包内容", "enterCryptocurrencyProof": "请输入加密货币支付凭证", "optionalMessage": "可选，有什么需要说明的可以在这里留言", "priceCalculation": "价格计算", "totalPrice": "总价", "calculating": "计算中...", "hasPendingRequest": "已有待处理的续费请求，请先取消或等待处理", "renewalRequestCreated": "续费请求创建成功", "renewalRequestCancelled": "续费请求已取消", "confirmCancelRequest": "确定要取消这个续费请求吗？", "confirmCancel": "确认取消", "failedToCreateRequest": "创建续费请求失败", "failedToCancelRequest": "取消续费请求失败", "failedToCalculatePrice": "计算价格失败", "paymentProofValidation": {"required": "请输入支付凭证", "cryptocurrencyIncomplete": "加密货币支付凭证信息不完整，请提供详细的交易信息"}, "renewalRequestValidation": {"selectDuration": "请选择续费时长", "selectDurationPlaceholder": "请选择续费时长", "selectPaymentMethod": "请选择支付方式", "enterPaymentProof": "请输入支付凭证"}, "renewalRequestForm": {"title": "提交续费请求", "price": "价格", "months": "个月", "monthlyRate": "月费", "totalAmount": "总金额", "submitRequest": "提交请求", "close": "关闭", "paymentAmountMismatch": "支付金额与计算价格不匹配，请重新计算价格", "formValidationFailed": "请检查表单填写是否正确", "alipayPlaceholder": "请输入支付宝口令红包内容，例如：￥123.45￥abc123", "cryptoPlaceholder": "请输入加密货币支付凭证，包含交易哈希、金额等信息", "alipayHint": "请确保口令红包金额与上方显示价格一致", "cryptoHint": "请提供完整的交易信息以便验证", "optionalMessage": "可选，有什么需要说明的可以在这里留言"}, "renewalRequestDetail": {"title": "续费请求详情", "submissionTime": "提交时间"}, "conversationHistory": "对话历史", "customer": "客户", "admin": "管理员", "sendMessage": "发送消息", "enterMessage": "输入消息内容...", "messageSent": "消息发送成功", "failedToSendMessage": "发送消息失败", "noMessagesYet": "暂无消息", "notificationMessages": {"permissionRequired": "续费请求功能需要高级订阅权限。请联系管理员升级您的订阅以使用此功能。", "featureNotAvailable": "功能不可用", "loadRenewalRequestsFailed": "加载续费请求失败"}}, "subscription": {"title": "订阅信息", "speedDetails": "速度详情", "validUntil": "有效期至", "nextReset": "下次重置", "bandwidth": "带宽", "trafficUsage": "流量使用情况", "unlimited": "无限制", "noDataAvailable": "没有可用的订阅数据", "serverStatus": "服务器状态", "refresh": "刷新"}, "subscriptionManagement": {"title": "订阅管理", "addSubscription": "添加订阅", "hideSearchFilters": "隐藏搜索过滤器", "showSearchFilters": "显示搜索过滤器", "hideSearch": "隐藏搜索", "showSearch": "显示搜索", "searchAndFilterOptions": "搜索和过滤选项", "id": "ID", "tokenId": "令牌ID", "email": "邮箱", "validUntil": "有效期至", "nextReset": "下次重置", "lines": "线路", "search": "搜索", "clear": "清除", "searchByExactId": "按确切ID搜索", "searchByExactTokenId": "按确切令牌ID搜索", "searchByEmail": "按邮箱搜索（部分匹配）", "startDate": "开始日期", "endDate": "结束日期", "to": "到", "selectLines": "选择线路", "traffic": "流量", "actions": "操作", "clickToCopy": "点击复制：", "lineCount": "线路数量", "maxIps": "最大IP数", "maxConnections": "最大连接数", "forwardEndpoint": "转发端点", "enabled": "已启用", "disabled": "已禁用", "view": "查看", "edit": "编辑", "extend": "延期", "resetTraffic": "重置流量", "delete": "删除", "addUser": "添加用户", "editUser": "编辑用户", "emailAddress": "邮箱地址", "bandwidth": "带宽", "trafficGB": "流量（GB）", "activateImmediately": "立即激活", "maxPortsPerServer": "每服务器最大端口数", "billingType": "计费类型", "cycleBilling": "周期计费", "onetimeBilling": "一次性计费", "cycleDays": "周期天数", "cyclePrice": "周期价格", "onetimePrice": "一次性价格", "totalDays": "总天数", "allowForwardEndpoint": "允许自带转发端点", "allowIpNum": "最大IP数", "allowConnNum": "每IP最大连接数", "leaveEmptyForUnlimited": "留空表示无限制", "maxUniqueIpsDescription": "可连接的唯一IP地址的最大数量", "maxConnectionsPerIpDescription": "每个IP地址的最大连接数", "cancel": "取消", "add": "添加", "update": "更新", "pleaseEnterEmailAddress": "请输入邮箱地址", "pleaseEnterValidEmailAddress": "请输入有效的邮箱地址", "pleaseEnterMaxPortsPerServer": "请输入每服务器最大端口数", "pleaseSelectAtLeastOneLine": "请至少选择一条线路", "confirmDelete": "您确定要删除此订阅吗？", "warning": "警告", "confirmExtend": "您确定要将此订阅延期 {days} 天吗？", "confirmResetTraffic": "您确定要重置此用户的流量吗？", "userAdded": "用户添加成功", "userUpdated": "用户更新成功", "userDeleted": "用户删除成功", "subscriptionExtended": "订阅延期成功", "trafficReset": "流量重置成功", "failedToAddUser": "添加用户失败", "failedToUpdateUser": "更新用户失败", "failedToDeleteUser": "删除用户失败", "failedToExtendSubscription": "延期订阅失败", "failedToResetTraffic": "重置流量失败", "failedToLoadSubscriptions": "加载订阅失败", "failedToLoadServers": "加载服务器失败", "tokenIdCopied": "令牌ID已复制到剪贴板", "failedToCopyTokenId": "复制令牌ID失败", "editSubscription": "编辑订阅", "cycle": "周期", "onetime": "一次性", "cycleDaysLabel": "周期天数", "cyclePriceLabel": "周期价格", "totalDaysLabel": "总天数", "onetimePriceLabel": "一次性价格", "trafficResetCycleLabel": "流量重置周期", "trafficResetCyclePlaceholder": "输入流量重置天数", "trafficResetCycleDescription": "流量重置的天数间隔，与订阅有效期独立", "linesLabel": "线路", "searchAndSelectLines": "搜索并选择线路", "maxIpsLabel": "最大IP数", "maxConnectionsPerIpLabel": "每IP最大连接数", "leaveEmptyUnlimited": "留空表示无限制", "activateImmediatelyLabel": "立即激活", "allowForwardEndpointLabel": "允许自带转发端点", "enterTrafficLimitGB": "输入流量限制（GB）", "enterEmailAddress": "输入邮箱地址", "packageLines": "套餐线路", "additionalLines": "额外线路", "includedInPackage": "套餐内", "selectAdditionalLines": "选择额外线路", "multiplier": "倍率", "trafficMultiplier": "流量倍率", "usingPackageConfig": "使用套餐配置", "packageConfigDescription": "已选择套餐，计费方式、线路配置等将从套餐中自动获取", "fieldManagedByPackage": "该字段由套餐管理，如需修改请编辑套餐配置", "editPackageConfig": "编辑套餐配置", "cycleBillingLabel": "周期计费", "onetimeBillingLabel": "一次性计费"}, "packageManagement": {"title": "套餐管理", "createPackage": "创建套餐", "editPackage": "编辑套餐", "packageName": "套餐名称", "displayName": "显示名称", "description": "套餐描述", "totalTraffic": "总流量", "bandwidth": "带宽限制", "maxPortsPerServer": "每服务器最大端口数", "allowForwardEndpoint": "允许自带转发端点", "allowIpNum": "允许IP数量", "allowConnNum": "每IP连接数限制", "isActive": "套餐状态", "isDefault": "默认套餐", "packageLines": "套餐线路", "lineCount": "线路数量", "status": "状态", "active": "激活", "inactive": "禁用", "default": "默认", "actions": "操作", "view": "查看", "edit": "编辑", "delete": "删除", "create": "创建", "update": "更新", "cancel": "取消", "save": "保存", "selectLine": "选择线路", "bandwidthLimit": "带宽限制(Mbps)", "trafficScale": "流量倍率", "lineTraffic": "线路流量(GB)", "addLine": "添加线路", "removeLine": "移除线路", "pleaseEnterPackageName": "请输入套餐名称", "pleaseEnterDisplayName": "请输入显示名称", "pleaseSelectAtLeastOneLine": "请至少选择一条线路", "packageNameExists": "套餐名称已存在", "trafficGB": "流量(GB)", "leaveEmptyForUnlimited": "留空表示无限制", "leaveEmptyForUnlimitedPackage": "留空表示不使用套餐限制，将使用系统默认配置", "packageCreatedSuccessfully": "套餐创建成功", "packageUpdatedSuccessfully": "套餐更新成功", "packageDeletedSuccessfully": "套餐删除成功", "requiredFields": "必填字段", "optionalFields": "可选配置", "confirmDeletePackage": "确定要删除这个套餐吗？", "cannotDeletePackageInUse": "无法删除正在使用的套餐", "failedToCreatePackage": "创建套餐失败", "failedToUpdatePackage": "更新套餐失败", "failedToDeletePackage": "删除套餐失败", "failedToLoadPackages": "加载套餐列表失败", "searchByName": "按名称搜索", "filterByStatus": "按状态筛选", "allStatuses": "所有状态", "onlyActive": "仅激活", "onlyInactive": "仅禁用", "onlyDefault": "仅默认", "allLinesSelected": "所有线路都已选择", "trafficScaleWarning": "线路倍率不为1时，流量限制需设置为倍率之后的值", "search": "搜索", "clear": "清除", "selectPackage": "选择套餐"}}, "latencyTest": {"title": "延迟测试", "testing": "测试中", "result": "延迟测试结果", "error": "延迟测试错误"}, "connectionStats": {"title": "连接统计", "loading": "获取中", "error": "连接统计错误", "errorMessage": "获取连接统计失败", "noForwarders": "直连", "columns": {"id": "连接ID", "startTime": "开始时间", "clientAddr": "客户端地址", "targetAddr": "目标地址", "connType": "类型", "forwarders": "转发器"}}, "theme": {"light": "浅色主题", "dark": "深色主题", "auto": "自动主题"}, "latencyMonitoring": {"title": "延迟监控", "addConfig": "添加监控配置", "realTimeStatus": "实时延迟状态", "historyData": "历史延迟数据", "configManagement": "监控配置", "refresh": "刷新", "noConfigs": "暂无监控配置", "testSuccess": "测试成功", "testFailed": "测试失败", "lastTest": "最后测试", "timeRanges": {"1h": "1小时", "3h": "3小时", "6h": "6小时", "12h": "12小时", "24h": "24小时", "7d": "7天"}, "configDialog": {"addTitle": "添加监控配置", "editTitle": "编辑监控配置", "displayName": "配置名称", "targetAddress": "目标地址", "testType": "测试类型", "targetPort": "目标端口", "packetSize": "包大小", "timeout": "超时时间", "interval": "测试间隔", "alertThreshold": "告警阈值", "isPublic": "所有用户可见", "isEnabled": "启用监控", "hints": {"targetAddress": "支持IP地址和域名", "tcpPort": "TCP测试需要指定端口", "packetSize": "ICMP包大小，默认64字节", "timeout": "测试超时时间，默认5000毫秒", "interval": "延迟测试间隔，默认60秒", "alertThreshold": "连续失败多少次后告警，默认3次", "isPublic": "勾选后，普通用户也可以查看此监控配置的数据", "isEnabled": "关闭后，将停止对此目标的延迟监控"}, "validation": {"displayNameRequired": "请输入配置名称", "displayNameLength": "名称长度在2到50个字符", "targetAddressRequired": "请输入目标地址", "targetAddressInvalid": "请输入有效的IP地址或域名", "testTypeRequired": "请选择测试类型", "tcpPortRequired": "TCP测试需要输入有效的端口号(1-65535)", "packetSizeRange": "包大小范围为8-1500字节", "timeoutRange": "超时时间范围为1000-30000毫秒", "intervalRange": "测试间隔范围为10-3600秒", "alertThresholdRange": "告警阈值范围为1-10次"}}, "table": {"name": "名称", "targetAddress": "目标地址", "testType": "测试类型", "interval": "间隔(秒)", "status": "状态", "actions": "操作", "edit": "编辑", "delete": "删除"}, "messages": {"configCreated": "配置创建成功", "configUpdated": "配置更新成功", "configDeleted": "删除成功", "statusUpdated": "配置状态更新成功", "loadRealtimeDataFailed": "加载实时数据失败", "loadHistoryDataFailed": "加载历史数据失败", "loadConfigsFailed": "加载配置失败", "updateStatusFailed": "更新配置状态失败", "deleteFailed": "删除失败", "createFailed": "创建失败", "updateFailed": "更新失败", "confirmDelete": "确定要删除监控配置 \"{name}\" 吗？"}}, "batchUpdate": {"title": "批量更新端口目标", "simpleMode": "简单模式", "advancedMode": "高级模式", "simpleModeDesc": "简单直观的操作，无需了解技术语法", "advancedModeDesc": "使用正则表达式进行复杂的模式匹配和替换", "replaceType": "替换类型", "selectReplaceType": "请选择替换类型", "regexPattern": "正则表达式", "replacement": "替换文本", "matchRule": "匹配规则", "newFormat": "新格式", "regexPlaceholder": "输入正则表达式，例如：192\\.168\\.1\\.(\\d+)", "replacementPlaceholder": "输入替换文本，例如：10.0.0.$1", "regexHelp": "使用正则表达式匹配需要更新的目标地址", "replacementHelp": "使用 $1, $2 等引用正则表达式中的捕获组", "types": {"ipAddress": "IP地址替换", "ipAddressDesc": "替换指定的IP地址，保持端口号不变", "port": "端口替换", "portDesc": "替换特定端口号，保持IP地址不变", "exact": "精确地址替换", "exactDesc": "精确替换完整地址 (IP:端口 格式)", "batchAdd": "批量增加", "batchAddDesc": "向现有端口添加新的目标地址", "batchRemove": "批量删除", "batchRemoveDesc": "从现有端口删除匹配的目标地址"}, "originalIpAddress": "原IP地址", "newIpAddress": "新IP地址", "originalIpAddressPlaceholder": "例如：*************", "newIpAddressPlaceholder": "例如：*********", "originalIpAddressHelp": "输入要替换的完整IP地址", "newIpAddressHelp": "输入新的IP地址，端口号将保持不变", "originalPort": "原端口", "newPort": "新端口", "originalPortPlaceholder": "例如：8080", "newPortPlaceholder": "例如：9080", "originalPortHelp": "输入要替换的端口号", "newPortHelp": "输入新的端口号，IP地址将保持不变", "originalAddress": "原地址", "newAddress": "新地址", "originalAddressPlaceholder": "例如：*************:8080", "newAddressPlaceholder": "例如：**********:9080", "originalAddressHelp": "输入要替换的完整地址 (IP:端口 格式)", "newAddressHelp": "输入新的完整地址 (IP:端口 格式)", "batchAddAddress": "新增地址", "removeAddress": "删除地址", "batchAddAddressPlaceholder": "例如：*************:8080", "removeAddressPlaceholder": "例如：*************:8080", "batchAddAddressHelp": "输入要添加的完整地址 (IP:端口 格式)", "removeAddressHelp": "输入要删除的完整地址 (IP:端口格式)", "conditionAddress": "条件地址", "conditionAddressPlaceholder": "例如：*************:8080（留空则对所有端口添加）", "conditionAddressHelp": "指定条件地址时，只有包含此地址的端口才会添加新地址；留空则对所有端口添加", "example": "示例", "preview": "预览更改", "execute": "执行更新", "previewResults": "预览结果", "executionResults": "执行结果", "affectedPorts": "找到 {count} 个匹配的端口", "portName": "端口名称", "changes": "变更内容", "status": "状态", "willUpdate": "将更新", "noChange": "无变更", "successCount": "成功更新：{count} 个", "failedCount": "失败：{count} 个", "updatedPortsDetails": "更新端口详情", "closeAndRefresh": "关闭并刷新", "noMatchingPorts": "没有端口匹配指定的模式", "noChangesNeeded": "此模式不会产生任何更改", "previewComplete": "预览完成，将更新 {count} 个端口", "executeSuccess": "成功更新了 {count} 个端口", "noPortsUpdated": "没有端口被更新", "executePartialFailure": "部分成功：{success} 个更新成功，{failed} 个失败", "previewFailed": "预览更改失败", "executeFailed": "执行更新失败", "validation": {"regexRequired": "请输入正则表达式", "replacementRequired": "请输入替换文本", "invalidRegex": "无效的正则表达式", "replaceTypeRequired": "请选择替换类型", "originalIpAddressRequired": "请输入原IP地址", "newIpAddressRequired": "请输入新IP地址", "invalidIpAddressFormat": "无效的IP地址格式，应该类似于 *************", "originalPortRequired": "请输入原端口", "newPortRequired": "请输入新端口", "invalidPortFormat": "无效的端口格式，应该只包含数字", "originalAddressRequired": "请输入原地址", "newAddressRequired": "请输入新地址", "invalidAddressFormat": "无效的地址格式，应该类似于 *************:8080", "batchAddAddressRequired": "请输入要添加的地址", "removeAddressRequired": "请输入要删除的地址", "cannotRemoveAllTargets": "删除后目标地址列表不能为空"}}, "brandName": "莹火虫的世界", "search": {"filterOptionsLabel": "搜索和过滤选项"}, "systemSettings": {"title": "系统设置", "description": "管理系统全局配置和用户权限设置", "sections": {"website": "网站配置", "userPermissions": "用户权限"}, "siteTitle": {"label": "网站标题", "description": "设置网站在不同语言环境下显示的标题", "zh": "中文", "en": "英文", "zhPlaceholder": "请输入中文网站标题", "enPlaceholder": "请输入英文网站标题"}, "allowLatencyMonitoring": {"label": "允许用户添加延迟监控", "description": "控制普通用户是否可以为服务器添加延迟监控配置"}}}