// This is your Prisma schema file for zf-auth-server
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "cargo prisma"
  output   = "../src/prisma.generated.rs"
}

datasource db {
  provider = "postgresql"
  url      = env("ZF_AUTH_DATABASE_URL")
}

model ControllerInstance {
  id               String    @id @default(cuid())
  name             String    @unique
  licenseExpiresAt DateTime
  
  /// API Key for instance-level authentication
  apiKey           String    @unique @default(cuid())
  
  /// Stores all feature flags and limits for this instance.
  /// Example: { "max_workers": 10, "enable_feature_x": true }
  entitlements     Json      @default("{}")

  // -- Pricing Information --
  /// Monthly subscription rate for this instance
  monthlyRate      String?
  /// One-time setup fee for this instance
  setupFee         String?

  // -- License Activation --
  /// When the license was first activated (first client connection)
  activatedAt      DateTime?

  // -- Fields for Concurrency Control --
  /// The IP address of the last client to successfully perform a heartbeat.
  lastSeenIp       String?
  /// The timestamp of the last successful heartbeat.
  lastSeenAt       DateTime?
  /// A unique ID for the current "active" session/lease.
  currentSessionId String?   @unique

  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  
  renewalCodes     RenewalCode[]
  renewalRequests  RenewalRequest[]

  @@index([name])
  @@index([licenseExpiresAt])
  @@index([currentSessionId])
  @@index([apiKey])
  @@index([activatedAt])
}

model RenewalCode {
  id                 String    @id @default(cuid())
  duration           Int       // License extension in days
  amount             String?   // Renewal amount/price
  createdAt          DateTime  @default(now())
  usedAt             DateTime?
  usedByInstanceId   String?
  usedByInstance     ControllerInstance? @relation(fields: [usedByInstanceId], references: [id])
  
  // 关联到续费请求
  renewalRequests    RenewalRequest[]

  @@index([usedAt])
  @@index([usedByInstanceId])
}

model RenewalRequest {
  id                     String    @id @default(cuid())
  paymentMethod          PaymentMethod
  paymentAmount          String    // 支付金额
  paymentProof           String    // 支付凭证(口令红包内容或加密货币凭证)
  customerMessage        String?   // 客户留言（保留兼容性）
  status                 RenewalRequestStatus @default(PENDING)
  requestedDuration      Int       // 请求的续费周期(月数)
  
  // 新增对话历史相关字段
  hasCustomerReplies     Boolean   @default(false) // 是否有客户回复
  lastMessageAt          DateTime? // 最后消息时间
  canReply               Boolean   @default(true)  // 是否允许回复
  
  // 关联字段
  instanceId             String
  instance               ControllerInstance @relation(fields: [instanceId], references: [id])
  
  // 处理信息
  processedAt            DateTime?
  processedBy            String?   // 处理管理员ID
  adminNotes             String?   // 管理员处理备注（保留兼容性）
  generatedRenewalCodeId String?   // 生成的续费码ID
  generatedRenewalCode   RenewalCode? @relation(fields: [generatedRenewalCodeId], references: [id])
  
  // 对话消息关联
  messages               RenewalRequestMessage[]
  
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt

  @@index([instanceId])
  @@index([status])
  @@index([createdAt])
  @@index([hasCustomerReplies])
  @@index([lastMessageAt])
}

enum PaymentMethod {
  ALIPAY_HONGBAO      // 支付宝口令红包
  CRYPTOCURRENCY      // 加密货币方式
}

enum RenewalRequestStatus {
  PENDING     // 待处理
  PROCESSED   // 已处理
  CANCELLED   // 已取消
  CLOSED_BY_ADMIN // 管理员关闭（不执行延期）
}

model RenewalRequestMessage {
  id                String    @id @default(cuid())
  renewalRequestId  String    // 关联的续费请求ID
  renewalRequest    RenewalRequest @relation(fields: [renewalRequestId], references: [id], onDelete: Cascade)
  messageType       MessageType    // 消息类型：客户或管理员
  content           String         // 消息内容
  authorId          String?        // 发送者ID（客户为null，管理员为admin ID）
  authorName        String?        // 发送者名称（显示用）
  createdAt         DateTime  @default(now())
  
  @@index([renewalRequestId])
  @@index([createdAt])
  @@index([messageType])
}

enum MessageType {
  CUSTOMER  // 客户消息
  ADMIN     // 管理员消息
}

model AdminUser {
  id             String    @id @default(cuid())
  username       String    @unique
  passwordHash   String
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  lastLoginAt    DateTime?

  @@index([username])
}