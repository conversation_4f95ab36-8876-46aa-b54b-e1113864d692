use crate::{
    crypto::{Jw<PERSON><PERSON><PERSON><PERSON>, Key<PERSON>anager},
    errors::AuthError,
    models::{AdminCloseRequestRequest, HeartbeatRequest, MessageResponse, SendMessageRequest},
    prisma::{
        admin_user, controller_instance, renewal_code, renewal_request, renewal_request_message,
        MessageType, PrismaClient,
    },
};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Duration, Utc};
use common::{CreateRenewalRequestRequest, PaginatedResponse, RenewalRequestResponse};
use prisma_client_rust::{operator::*, QueryError};
use std::sync::Arc;
use uuid::Uuid;

pub struct AuthService {
    db: Arc<PrismaClient>,
    pub key_manager: Arc<KeyManager>,
    jwt_expiry_hours: u64,
    session_timeout_hours: u64,
}

impl AuthService {
    pub fn new(
        db: Arc<PrismaClient>,
        key_manager: Arc<KeyManager>,
        jwt_expiry_hours: u64,
        session_timeout_hours: u64,
    ) -> Self {
        Self {
            db,
            key_manager,
            jwt_expiry_hours,
            session_timeout_hours,
        }
    }

    pub async fn heartbeat(
        &self,
        request: HeartbeatRequest,
        client_ip: String,
        existing_jwt: Option<String>,
        websocket_registry: Option<&Arc<crate::websocket::ConnectionRegistry>>,
    ) -> Result<String, AuthError> {
        let now = Utc::now();

        // Find the instance
        let instance = self
            .db
            .controller_instance()
            .find_unique(controller_instance::id::equals(request.instance_id.clone()))
            .exec()
            .await?
            .ok_or(AuthError::InstanceNotFound)?;

        // Handle license activation and expiration
        let (instance, _should_update_license) = if instance.activated_at.is_none() {
            // License not yet activated - activate it on first connection
            let activated_at = now;
            let new_expires_at = activated_at
                + Duration::days(
                    (instance
                        .license_expires_at
                        .signed_duration_since(instance.created_at))
                    .num_days(),
                );

            log::info!(
                "Activating license for instance {} at {}",
                request.instance_id,
                activated_at
            );

            let updated_instance = self
                .db
                .controller_instance()
                .update(
                    controller_instance::id::equals(request.instance_id.clone()),
                    vec![
                        controller_instance::activated_at::set(Some(activated_at.into())),
                        controller_instance::license_expires_at::set(new_expires_at.into()),
                    ],
                )
                .exec()
                .await?;
            (updated_instance, false)
        } else {
            // License already activated - check if it's expired
            if instance.license_expires_at < now {
                return Err(AuthError::LicenseExpired);
            }
            (instance, false)
        };

        // Extract session ID from existing JWT if present
        let existing_session_id = if let Some(jwt) = existing_jwt {
            match self.key_manager.verify_jwt(&jwt) {
                Ok(claims) => Some(claims.sid),
                Err(_) => None, // Invalid JWT, treat as new session
            }
        } else {
            None
        };

        // Concurrency check
        if let Some(current_session_id) = &instance.current_session_id {
            if let Some(last_seen_at) = instance.last_seen_at {
                let session_timeout = Duration::hours(self.session_timeout_hours as i64);
                let is_session_active = now.signed_duration_since(last_seen_at) < session_timeout;

                if is_session_active {
                    // Check if WebSocket connection actually exists
                    let websocket_exists = if let Some(registry) = websocket_registry {
                        registry.get_connection(current_session_id).await.is_some()
                    } else {
                        true // Assume exists if we can't check
                    };

                    if !websocket_exists {
                        // Database session exists but no WebSocket connection - clear it
                        log::info!(
                            "Clearing stale database session for instance {} (session: {})",
                            request.instance_id,
                            current_session_id
                        );
                        // Continue with creating new session (don't return error)
                    } else {
                        // Session is still active, check if this is the same client
                        if let Some(ref existing_sid) = existing_session_id {
                            if existing_sid != current_session_id {
                                return Err(AuthError::LicenseInUse);
                            }
                        } else {
                            // No existing session ID provided, but there's an active session
                            return Err(AuthError::LicenseInUse);
                        }
                    }
                }
            }
        }

        // Generate new session ID if needed
        let session_id = existing_session_id.unwrap_or_else(|| Uuid::new_v4().to_string());

        // Update instance with new session information
        let updated_instance = self
            .db
            .controller_instance()
            .update(
                controller_instance::id::equals(instance.id.clone()),
                vec![
                    controller_instance::last_seen_ip::set(Some(client_ip)),
                    controller_instance::last_seen_at::set(Some(now.into())),
                    controller_instance::current_session_id::set(Some(session_id.clone())),
                ],
            )
            .exec()
            .await?;

        // Create JWT
        let exp = (now + Duration::hours(self.jwt_expiry_hours as i64)).timestamp() as u64;
        let claims = JwtClaims {
            sub: instance.id,
            sid: session_id,
            exp,
            iat: now.timestamp() as u64,
            entitlements: updated_instance.entitlements,
        };

        let token = self.key_manager.create_jwt(&claims)?;
        Ok(token)
    }

    pub async fn create_instance(
        &self,
        name: String,
        initial_duration_days: i32,
        entitlements: serde_json::Value,
        monthly_rate: Option<String>,
        setup_fee: Option<String>,
    ) -> Result<crate::prisma::controller_instance::Data, AuthError> {
        use crate::prisma::controller_instance;

        // Store the initial duration as a placeholder expiration date
        // The actual expiration will be calculated when the license is activated
        let placeholder_expires_at = Utc::now() + Duration::days(initial_duration_days as i64);

        let mut params = vec![controller_instance::entitlements::set(entitlements)];

        // Add monthly rate if provided
        if let Some(rate) = monthly_rate {
            if let Ok(decimal_rate) = rate.parse::<f64>() {
                params.push(controller_instance::monthly_rate::set(Some(
                    decimal_rate.to_string(),
                )));
            }
        }

        // Add setup fee if provided
        if let Some(fee) = setup_fee {
            if let Ok(decimal_fee) = fee.parse::<f64>() {
                params.push(controller_instance::setup_fee::set(Some(
                    decimal_fee.to_string(),
                )));
            }
        }

        let instance = self
            .db
            .controller_instance()
            .create(name, placeholder_expires_at.into(), params)
            .exec()
            .await?;

        Ok(instance)
    }

    pub async fn get_all_instances(
        &self,
    ) -> Result<Vec<crate::prisma::controller_instance::Data>, AuthError> {
        let instances = self
            .db
            .controller_instance()
            .find_many(vec![])
            .order_by(controller_instance::id::order(
                prisma_client_rust::Direction::Desc,
            ))
            .exec()
            .await?;

        Ok(instances)
    }

    pub async fn batch_create_instances(
        &self,
        name_template: String,
        initial_duration: i32,
        entitlements: serde_json::Value,
        quantity: i32,
    ) -> Result<Vec<crate::prisma::controller_instance::Data>, AuthError> {
        let now = chrono::Utc::now();
        let placeholder_expires_at = now + chrono::Duration::days(initial_duration as i64);
        let mut instances = Vec::new();

        for i in 1..=quantity {
            let instance_name = if name_template.contains("{{index}}") {
                // If template contains {{index}}, replace it with the current index
                name_template.replace("{{index}}", &i.to_string())
            } else {
                // If template doesn't contain {{index}}, append the index to ensure uniqueness
                format!("{}-{}", name_template, i)
            };

            let instance = self
                .db
                .controller_instance()
                .create(
                    instance_name,
                    placeholder_expires_at.into(),
                    vec![controller_instance::entitlements::set(entitlements.clone())],
                )
                .exec()
                .await?;
            instances.push(instance);
        }

        Ok(instances)
    }

    pub async fn get_instances_paginated(
        &self,
        page: u32,
        page_size: u32,
        search: Option<String>,
    ) -> Result<(Vec<crate::prisma::controller_instance::Data>, u32), AuthError> {
        let skip = ((page - 1) * page_size) as i64;
        let take = page_size as i64;

        // Build the where clause for search
        let where_clause = if let Some(search_term) = search {
            vec![or(vec![
                controller_instance::name::contains(search_term.clone()),
                controller_instance::id::contains(search_term),
            ])]
        } else {
            vec![]
        };

        // Get the total count first
        let total_count = self
            .db
            .controller_instance()
            .count(where_clause.clone())
            .exec()
            .await? as u32;

        // Get the paginated instances
        let instances = self
            .db
            .controller_instance()
            .find_many(where_clause)
            .skip(skip)
            .take(take)
            .order_by(controller_instance::id::order(
                prisma_client_rust::Direction::Desc,
            ))
            .exec()
            .await?;

        Ok((instances, total_count))
    }

    pub async fn update_instance(
        &self,
        id: String,
        name: Option<String>,
        entitlements: Option<serde_json::Value>,
        monthly_rate: Option<String>,
        setup_fee: Option<String>,
    ) -> Result<crate::prisma::controller_instance::Data, AuthError> {
        let mut updates = vec![];

        if let Some(name) = name {
            updates.push(controller_instance::name::set(name));
        }

        if let Some(entitlements) = entitlements {
            updates.push(controller_instance::entitlements::set(entitlements));
        }
        if let Some(monthly_rate) = monthly_rate {
            updates.push(controller_instance::monthly_rate::set(Some(monthly_rate)));
        }
        if let Some(setup_fee) = setup_fee {
            updates.push(controller_instance::setup_fee::set(Some(setup_fee)));
        }

        if updates.is_empty() {
            // If no updates, fetch and return current instance
            let instance = self
                .db
                .controller_instance()
                .find_unique(controller_instance::id::equals(id))
                .exec()
                .await?
                .ok_or_else(|| AuthError::InstanceNotFound)?;
            return Ok(instance);
        }

        let updated_instance = self
            .db
            .controller_instance()
            .update(controller_instance::id::equals(id), updates)
            .exec()
            .await?;

        Ok(updated_instance)
    }

    pub async fn delete_instance(&self, id: String) -> Result<(), AuthError> {
        self.db
            .controller_instance()
            .delete(controller_instance::id::equals(id))
            .exec()
            .await?;
        Ok(())
    }

    pub async fn force_expire_session(&self, id: String) -> Result<(), AuthError> {
        self.db
            .controller_instance()
            .update(
                controller_instance::id::equals(id),
                vec![controller_instance::current_session_id::set(None)],
            )
            .exec()
            .await?;
        Ok(())
    }

    pub async fn create_renewal_code(&self, duration: i32) -> Result<String, AuthError> {
        let code = self
            .db
            .renewal_code()
            .create(duration, vec![])
            .exec()
            .await?;

        Ok(code.id)
    }

    pub async fn generate_renewal_codes(
        &self,
        duration: i32,
        quantity: i32,
        amount: Option<String>,
    ) -> Result<Vec<crate::prisma::renewal_code::Data>, AuthError> {
        use crate::prisma::renewal_code;

        let mut codes = Vec::new();

        // Prepare optional amount parameter
        let mut params = vec![];
        if let Some(amt) = amount {
            if let Ok(decimal_amt) = amt.parse::<f64>() {
                params.push(renewal_code::amount::set(Some(decimal_amt.to_string())));
            }
        }

        for _ in 0..quantity {
            let code = self
                .db
                .renewal_code()
                .create(duration, params.clone())
                .exec()
                .await?;
            codes.push(code);
        }
        Ok(codes)
    }

    pub async fn delete_renewal_code(&self, id: String) -> Result<(), AuthError> {
        self.db
            .renewal_code()
            .delete(renewal_code::id::equals(id))
            .exec()
            .await?;
        Ok(())
    }

    pub async fn apply_renewal_code(
        &self,
        instance_id: String,
        renewal_code_id: String,
    ) -> Result<(), AuthError> {
        // Get the renewal code
        let renewal_code = self
            .db
            .renewal_code()
            .find_unique(crate::prisma::renewal_code::id::equals(
                renewal_code_id.clone(),
            ))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("Renewal code not found".to_string()))?;

        // Check if already used
        if renewal_code.used_at.is_some() {
            return Err(AuthError::BadRequest(
                "Renewal code already used".to_string(),
            ));
        }

        // Get the instance
        let instance = self
            .db
            .controller_instance()
            .find_unique(controller_instance::id::equals(instance_id.clone()))
            .exec()
            .await?
            .ok_or(AuthError::InstanceNotFound)?;

        // Calculate new expiry date
        let now = Utc::now();
        let new_expiry = if instance.license_expires_at < now {
            // License has expired, calculate from current time
            now + Duration::days(renewal_code.duration as i64)
        } else {
            // License is still valid, extend from current expiry
            instance.license_expires_at.with_timezone(&Utc)
                + Duration::days(renewal_code.duration as i64)
        };

        // Update both the instance and the renewal code

        self.db
            ._transaction()
            .run(move |tx| async move {
                let now = Utc::now();

                // Update instance
                tx.controller_instance()
                    .update(
                        controller_instance::id::equals(instance_id.clone()),
                        vec![controller_instance::license_expires_at::set(
                            new_expiry.into(),
                        )],
                    )
                    .exec()
                    .await?;

                // Mark renewal code as used
                tx.renewal_code()
                    .update(
                        crate::prisma::renewal_code::id::equals(renewal_code_id),
                        vec![
                            crate::prisma::renewal_code::used_at::set(Some(now.into())),
                            crate::prisma::renewal_code::used_by_instance_id::set(Some(
                                instance_id,
                            )),
                        ],
                    )
                    .exec()
                    .await?;

                Ok::<_, QueryError>(())
            })
            .await
            .map_err(|e| {
                AuthError::Internal(anyhow::anyhow!("Failed to apply renewal code: {}", e))
            })?;

        Ok(())
    }

    pub async fn get_all_renewal_codes(
        &self,
    ) -> Result<Vec<crate::prisma::renewal_code::Data>, AuthError> {
        let codes = self
            .db
            .renewal_code()
            .find_many(vec![])
            .order_by(renewal_code::id::order(prisma_client_rust::Direction::Desc))
            .exec()
            .await?;

        Ok(codes)
    }

    pub async fn get_all_renewal_codes_available(
        &self,
    ) -> Result<Vec<crate::prisma::renewal_code::Data>, AuthError> {
        let codes = self
            .db
            .renewal_code()
            .find_many(vec![renewal_code::used_at::equals(None)])
            .order_by(renewal_code::id::order(prisma_client_rust::Direction::Desc))
            .exec()
            .await?;

        Ok(codes)
    }

    pub async fn get_renewal_codes_paginated(
        &self,
        page: u32,
        page_size: u32,
        status: Option<String>,
    ) -> Result<(Vec<crate::prisma::renewal_code::Data>, u32), AuthError> {
        let skip = ((page - 1) * page_size) as i64;
        let take = page_size as i64;

        // Build the where clause for status filter
        let where_clause = if let Some(status_filter) = status {
            match status_filter.as_str() {
                "available" => vec![renewal_code::used_at::equals(None)],
                "used" => vec![renewal_code::used_at::not(None)],
                _ => vec![], // Invalid status, return all
            }
        } else {
            vec![]
        };

        // Get the total count first
        let total_count = self
            .db
            .renewal_code()
            .count(where_clause.clone())
            .exec()
            .await? as u32;

        // Get the paginated renewal codes
        let codes = self
            .db
            .renewal_code()
            .find_many(where_clause)
            .skip(skip)
            .take(take)
            .order_by(renewal_code::id::order(prisma_client_rust::Direction::Desc))
            .exec()
            .await?;

        Ok((codes, total_count))
    }

    // Admin user management methods
    pub async fn is_initialized(&self) -> Result<bool, AuthError> {
        let admin_count = self.db.admin_user().count(vec![]).exec().await?;

        Ok(admin_count > 0)
    }

    pub async fn create_admin_user(
        &self,
        username: String,
        password: String,
    ) -> Result<String, AuthError> {
        // Check if any admin user already exists
        if self.is_initialized().await? {
            return Err(AuthError::BadRequest(
                "Admin user already exists".to_string(),
            ));
        }

        // Hash the password
        let password_hash = hash(password, DEFAULT_COST)
            .map_err(|_| AuthError::BadRequest("Failed to hash password".to_string()))?;

        // Create the admin user
        let admin_user = self
            .db
            .admin_user()
            .create(username, password_hash, vec![])
            .exec()
            .await?;

        Ok(admin_user.id)
    }

    pub async fn admin_login(
        &self,
        username: String,
        password: String,
    ) -> Result<String, AuthError> {
        // Find the admin user
        let admin_user = self
            .db
            .admin_user()
            .find_unique(admin_user::username::equals(username))
            .exec()
            .await?
            .ok_or(AuthError::Unauthorized)?;

        // Verify password
        if !verify(&password, &admin_user.password_hash)
            .map_err(|_| AuthError::BadRequest("Failed to verify password".to_string()))?
        {
            return Err(AuthError::Unauthorized);
        }

        // Update last login time
        let now = Utc::now();
        self.db
            .admin_user()
            .update(
                admin_user::id::equals(admin_user.id.clone()),
                vec![admin_user::last_login_at::set(Some(now.into()))],
            )
            .exec()
            .await?;

        // Create admin JWT
        let exp = (now + Duration::hours(24)).timestamp() as u64; // 24 hour expiry for admin tokens
        let claims = JwtClaims {
            sub: admin_user.id,
            sid: Uuid::new_v4().to_string(),
            exp,
            iat: now.timestamp() as u64,
            entitlements: serde_json::json!({"admin": true}),
        };

        let token = self.key_manager.create_jwt(&claims)?;
        Ok(token)
    }

    pub async fn verify_admin_token(&self, token: &str) -> Result<bool, AuthError> {
        let claims = self.key_manager.verify_jwt(token)?;

        // Check if the token has admin entitlements
        if let Some(admin) = claims.entitlements.get("admin") {
            if admin.as_bool() == Some(true) {
                return Ok(true);
            }
        }

        Ok(false)
    }

    pub async fn get_admin_user(
        &self,
        id: String,
    ) -> Result<crate::prisma::admin_user::Data, AuthError> {
        let admin_user = self
            .db
            .admin_user()
            .find_unique(admin_user::id::equals(id))
            .exec()
            .await?
            .ok_or(AuthError::BadRequest("Admin user not found".to_string()))?;

        Ok(admin_user)
    }

    pub async fn get_dashboard_stats(
        &self,
    ) -> Result<crate::models::DashboardStatsResponse, AuthError> {
        let now = Utc::now();
        let near_expiration_threshold = now + Duration::days(7);

        // Get all instances
        let instances = self.get_all_instances().await?;

        let mut active_instances = 0;
        let mut expired_instances = 0;
        let mut near_expiration = 0;
        let mut pending_instances = 0;

        for instance in instances {
            if instance.activated_at.is_none() {
                // License not yet activated
                pending_instances += 1;
            } else if instance.license_expires_at < now {
                // License is expired
                expired_instances += 1;
            } else if instance.license_expires_at < near_expiration_threshold {
                // License will expire soon
                near_expiration += 1;
            } else {
                // License is active and healthy
                active_instances += 1;
            }
        }

        // Get available renewal codes
        let renewal_codes = self.get_all_renewal_codes_available().await?;
        let available_codes = renewal_codes.len() as u64;

        Ok(crate::models::DashboardStatsResponse {
            active_instances,
            expired_instances,
            near_expiration,
            pending_instances,
            available_codes,
        })
    }

    // Get instance by ID (for instance-level API)
    pub async fn get_instance_by_id(
        &self,
        instance_id: String,
    ) -> Result<crate::prisma::controller_instance::Data, AuthError> {
        let instance = self
            .db
            .controller_instance()
            .find_unique(crate::prisma::controller_instance::id::equals(instance_id))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("Instance not found".to_string()))?;
        Ok(instance)
    }

    // Get renewal history for a specific instance
    pub async fn get_instance_renewal_history(
        &self,
        instance_id: String,
    ) -> Result<Vec<crate::models::RenewalHistoryItem>, AuthError> {
        // Query renewal codes that have been used by this instance
        let used_codes = self
            .db
            .renewal_code()
            .find_many(vec![
                crate::prisma::renewal_code::used_by_instance_id::equals(Some(instance_id.clone())),
                crate::prisma::renewal_code::used_at::not(None),
            ])
            .order_by(renewal_code::used_at::order(
                prisma_client_rust::Direction::Desc,
            ))
            .exec()
            .await?;

        // Convert to RenewalHistoryItem format
        let mut history = Vec::new();
        for code in used_codes {
            if let Some(used_at) = code.used_at {
                // We need to calculate the new expiry date by querying the instance
                // and subtracting the duration from the current expiry
                let instance = self.get_instance_by_id(instance_id.clone()).await?;
                let new_expiry_date = instance.license_expires_at;

                history.push(crate::models::RenewalHistoryItem {
                    date: used_at.into(),
                    code_id: code.id,
                    extended_days: code.duration,
                    new_expiry_date: new_expiry_date.into(),
                    status: "applied".to_string(),
                });
            }
        }

        // Sort by date (newest first)
        history.sort_by(|a, b| b.date.cmp(&a.date));

        Ok(history)
    }

    // Verify instance API key
    pub async fn verify_instance_api_key(
        &self,
        instance_id: &str,
        api_key: &str,
    ) -> Result<(), AuthError> {
        // Get the instance from database
        let instance = self
            .db
            .controller_instance()
            .find_unique(crate::prisma::controller_instance::id::equals(
                instance_id.to_string(),
            ))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("Instance not found".to_string()))?;

        // Verify the API key matches the stored one
        if api_key != instance.api_key {
            return Err(AuthError::Unauthorized);
        }

        Ok(())
    }

    // Clear database session if it matches the provided session ID
    pub async fn clear_session_if_matches(
        &self,
        instance_id: String,
        session_id: String,
    ) -> Result<(), AuthError> {
        // Only clear if the database session matches the provided session
        let instance = self
            .db
            .controller_instance()
            .find_unique(controller_instance::id::equals(instance_id.clone()))
            .exec()
            .await?;

        if let Some(instance) = instance {
            if let Some(current_session_id) = &instance.current_session_id {
                if current_session_id == &session_id {
                    // Clear the session from database
                    self.db
                        .controller_instance()
                        .update(
                            controller_instance::id::equals(instance_id),
                            vec![controller_instance::current_session_id::set(None)],
                        )
                        .exec()
                        .await?;
                }
            }
        }

        Ok(())
    }

    // 价格计算相关方法

    // 计算续费价格
    pub async fn calculate_renewal_price(
        &self,
        instance_id: String,
        requested_duration: i32,
    ) -> Result<crate::models::CalculateRenewalPriceResponse, AuthError> {
        log::info!(
            "Calculating renewal price for instance: {}, duration: {} months",
            instance_id,
            requested_duration
        );

        // 获取实例信息
        let instance = self
            .db
            .controller_instance()
            .find_unique(controller_instance::id::equals(instance_id.clone()))
            .exec()
            .await?
            .ok_or_else(|| {
                log::error!("Instance not found: {}", instance_id);
                AuthError::BadRequest("Instance not found".to_string())
            })?;

        log::info!(
            "Found instance: {}, monthly_rate: {:?}",
            instance_id,
            instance.monthly_rate
        );

        // 获取月费价格
        let monthly_rate = instance.monthly_rate.ok_or_else(|| {
            log::error!("No monthly rate set for instance: {}", instance_id);
            AuthError::BadRequest("No monthly rate set for this instance".to_string())
        })?;

        // 解析月费为数值
        let monthly_rate_value: f64 = monthly_rate
            .parse()
            .map_err(|_| AuthError::Internal(anyhow::anyhow!("Invalid monthly rate format")))?;

        // 计算总价格
        let total_price = monthly_rate_value * requested_duration as f64;

        Ok(crate::models::CalculateRenewalPriceResponse {
            monthly_rate,
            requested_duration,
            total_price: total_price.to_string(),
            currency: "CNY".to_string(),
        })
    }

    // 续费请求相关方法

    // 创建续费请求
    pub async fn create_renewal_request(
        &self,
        instance_id: String,
        request: CreateRenewalRequestRequest,
    ) -> Result<RenewalRequestResponse, AuthError> {
        use crate::prisma::{renewal_request, PaymentMethod, RenewalRequestStatus};

        // 验证实例存在
        let instance = self
            .db
            .controller_instance()
            .find_unique(controller_instance::id::equals(instance_id.clone()))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("Instance not found".to_string()))?;

        // 检查是否已有pending状态的续费请求
        let existing_pending = self
            .db
            .renewal_request()
            .find_first(vec![
                renewal_request::instance_id::equals(instance_id.clone()),
                renewal_request::status::equals(RenewalRequestStatus::Pending),
            ])
            .exec()
            .await?;

        if existing_pending.is_some() {
            return Err(AuthError::BadRequest(
                "已存在待处理的续费请求，请先取消或等待处理完成".to_string(),
            ));
        }

        // 转换支付方式
        let payment_method = match request.payment_method.as_str() {
            "alipay_hongbao" => PaymentMethod::AlipayHongbao,
            "cryptocurrency" => {
                //PaymentMethod::Cryptocurrency,
                return Err(AuthError::BadRequest(
                    "当前暂没有实现加密货币付款".to_string(),
                ));
            }
            _ => return Err(AuthError::BadRequest("无效的支付方式".to_string())),
        };

        // 使用事务创建续费请求和初始消息
        let now = Utc::now();
        let has_customer_message = request.customer_message.is_some()
            && !request.customer_message.as_ref().unwrap().trim().is_empty();

        let renewal_request: crate::prisma::renewal_request::Data = self
            .db
            ._transaction()
            .run(|tx| async move {
                // 创建续费请求
                let renewal_request = tx
                    .renewal_request()
                    .create(
                        payment_method,
                        request.payment_amount.clone(),
                        request.payment_proof.clone(),
                        request.requested_duration,
                        controller_instance::id::equals(instance_id.clone()),
                        vec![
                            renewal_request::customer_message::set(
                                request.customer_message.clone(),
                            ),
                            renewal_request::has_customer_replies::set(has_customer_message),
                            renewal_request::last_message_at::set(if has_customer_message {
                                Some(now.into())
                            } else {
                                None
                            }),
                            renewal_request::can_reply::set(true),
                        ],
                    )
                    .exec()
                    .await?;

                // 如果有客户留言，创建第一条消息
                if has_customer_message {
                    tx.renewal_request_message()
                        .create(
                            renewal_request::id::equals(renewal_request.id.clone()),
                            MessageType::Customer,
                            request.customer_message.clone().unwrap(),
                            vec![
                                renewal_request_message::author_id::set(None),
                                renewal_request_message::author_name::set(Some(
                                    "Customer".to_string(),
                                )),
                            ],
                        )
                        .exec()
                        .await?;
                }

                Ok(renewal_request)
            })
            .await
            .map_err(|e: QueryError| {
                AuthError::Internal(anyhow::anyhow!("Failed to create renewal request: {}", e))
            })?;

        Ok(RenewalRequestResponse {
            id: renewal_request.id,
            payment_method: renewal_request.payment_method.to_string().to_lowercase(),
            payment_amount: renewal_request.payment_amount,
            payment_proof: renewal_request.payment_proof,
            customer_message: renewal_request.customer_message,
            status: renewal_request.status.to_string(),
            requested_duration: renewal_request.requested_duration,
            instance_id: renewal_request.instance_id,
            instance_name: Some(instance.name),
            processed_at: renewal_request
                .processed_at
                .map(|dt| dt.with_timezone(&Utc)),
            processed_by: renewal_request.processed_by,
            admin_notes: None, // Not available in create operation
            generated_renewal_code_id: renewal_request.generated_renewal_code_id,
            has_customer_replies: has_customer_message,
            last_message_at: if has_customer_message {
                Some(now)
            } else {
                None
            },
            can_reply: true,
            messages: None, // Not included in create operation
            created_at: renewal_request.created_at.into(),
            updated_at: renewal_request.updated_at.into(),
        })
    }

    // 获取续费请求列表
    pub async fn get_renewal_requests(
        &self,
        query: crate::models::RenewalRequestQuery,
    ) -> Result<PaginatedResponse<RenewalRequestResponse>, AuthError> {
        use crate::prisma::{renewal_request, RenewalRequestStatus};

        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(20);
        let skip = ((page - 1) * page_size) as i64;

        let mut where_conditions = Vec::new();

        // 按状态过滤
        if let Some(status) = &query.status {
            log::info!("Filtering by status: '{}'", status);
            let status_enum = match status.to_uppercase().as_str() {
                "PENDING" => RenewalRequestStatus::Pending,
                "PROCESSED" => RenewalRequestStatus::Processed,
                "CANCELLED" => RenewalRequestStatus::Cancelled,
                "CLOSED_BY_ADMIN" => RenewalRequestStatus::ClosedByAdmin,
                _ => {
                    log::error!("Invalid status value received: '{}'", status);
                    return Err(AuthError::BadRequest("无效的状态".to_string()));
                }
            };
            where_conditions.push(renewal_request::status::equals(status_enum));
        }

        // 按hasCustomerReplies过滤
        if let Some(has_customer_replies) = &query.has_customer_replies {
            where_conditions.push(renewal_request::has_customer_replies::equals(
                *has_customer_replies,
            ));
        }

        // 按实例ID过滤
        if let Some(instance_id) = &query.instance_id {
            where_conditions.push(renewal_request::instance_id::equals(instance_id.clone()));
        }

        // 获取总数
        let total = self
            .db
            .renewal_request()
            .count(where_conditions.clone())
            .exec()
            .await? as u32;

        // 获取数据，包含实例信息
        let requests = self
            .db
            .renewal_request()
            .find_many(where_conditions)
            .skip(skip)
            .take(page_size as i64)
            .order_by(renewal_request::created_at::order(
                prisma_client_rust::Direction::Desc,
            ))
            .include(renewal_request::include!({ instance }))
            .exec()
            .await?;

        let response_data: Vec<RenewalRequestResponse> = requests
            .into_iter()
            .map(|req| RenewalRequestResponse {
                id: req.id,
                payment_method: req.payment_method.to_string().to_lowercase(),
                payment_amount: req.payment_amount,
                payment_proof: req.payment_proof,
                customer_message: req.customer_message,
                status: req.status.to_string(),
                requested_duration: req.requested_duration,
                instance_id: req.instance_id,
                instance_name: Some(req.instance.name),
                processed_at: req.processed_at.map(|dt| dt.with_timezone(&Utc)),
                processed_by: req.processed_by,
                admin_notes: req.admin_notes,
                generated_renewal_code_id: req.generated_renewal_code_id,
                has_customer_replies: req.has_customer_replies,
                last_message_at: req.last_message_at.map(|dt| dt.with_timezone(&Utc)),
                can_reply: req.can_reply,
                messages: None,
                created_at: req.created_at.into(),
                updated_at: req.updated_at.into(),
            })
            .collect();

        let total_pages = (total + page_size - 1) / page_size;

        Ok(PaginatedResponse {
            data: response_data,
            page,
            page_size,
            total,
            total_pages,
        })
    }

    // 根据ID获取续费请求详情
    pub async fn get_renewal_request_by_id(
        &self,
        request_id: String,
    ) -> Result<RenewalRequestResponse, AuthError> {
        use crate::prisma::renewal_request;

        let renewal_request = self
            .db
            .renewal_request()
            .find_unique(renewal_request::id::equals(request_id))
            .include(renewal_request::include!({ instance }))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("续费请求不存在".to_string()))?;

        Ok(RenewalRequestResponse {
            id: renewal_request.id,
            payment_method: renewal_request.payment_method.to_string().to_lowercase(),
            payment_amount: renewal_request.payment_amount,
            payment_proof: renewal_request.payment_proof,
            customer_message: renewal_request.customer_message,
            status: renewal_request.status.to_string(),
            requested_duration: renewal_request.requested_duration,
            instance_id: renewal_request.instance_id,
            instance_name: Some(renewal_request.instance.name),
            processed_at: renewal_request
                .processed_at
                .map(|dt| dt.with_timezone(&Utc)),
            processed_by: renewal_request.processed_by,
            admin_notes: renewal_request.admin_notes,
            generated_renewal_code_id: renewal_request.generated_renewal_code_id,
            has_customer_replies: renewal_request.has_customer_replies,
            last_message_at: renewal_request
                .last_message_at
                .map(|dt| dt.with_timezone(&Utc)),
            can_reply: renewal_request.can_reply,
            messages: None,
            created_at: renewal_request.created_at.into(),
            updated_at: renewal_request.updated_at.into(),
        })
    }

    // 取消续费请求
    pub async fn cancel_renewal_request(
        &self,
        instance_id: String,
        request_id: String,
    ) -> Result<RenewalRequestResponse, AuthError> {
        use crate::prisma::{renewal_request, RenewalRequestStatus};

        let renewal_request = self
            .db
            .renewal_request()
            .find_unique(renewal_request::id::equals(request_id.clone()))
            .include(renewal_request::include!({ instance }))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("续费请求不存在".to_string()))?;

        // 验证请求属于该实例
        if renewal_request.instance_id != instance_id {
            return Err(AuthError::Unauthorized);
        }

        // 只能取消pending状态的请求
        if renewal_request.status != RenewalRequestStatus::Pending {
            return Err(AuthError::BadRequest(
                "只能取消待处理状态的续费请求".to_string(),
            ));
        }

        // 更新状态为已取消
        let updated_request = self
            .db
            .renewal_request()
            .update(
                renewal_request::id::equals(request_id),
                vec![
                    renewal_request::status::set(RenewalRequestStatus::Cancelled),
                    renewal_request::can_reply::set(false),
                ],
            )
            .include(renewal_request::include!({ instance }))
            .exec()
            .await?;

        Ok(RenewalRequestResponse {
            id: updated_request.id,
            payment_method: updated_request.payment_method.to_string().to_lowercase(),
            payment_amount: updated_request.payment_amount,
            payment_proof: updated_request.payment_proof,
            customer_message: updated_request.customer_message,
            status: updated_request.status.to_string(),
            requested_duration: updated_request.requested_duration,
            instance_id: updated_request.instance_id,
            instance_name: Some(updated_request.instance.name),
            processed_at: updated_request
                .processed_at
                .map(|dt| dt.with_timezone(&Utc)),
            processed_by: updated_request.processed_by,
            admin_notes: updated_request.admin_notes,
            generated_renewal_code_id: updated_request.generated_renewal_code_id,
            has_customer_replies: updated_request.has_customer_replies,
            last_message_at: updated_request
                .last_message_at
                .map(|dt| dt.with_timezone(&Utc)),
            can_reply: updated_request.can_reply,
            messages: None,
            created_at: updated_request.created_at.into(),
            updated_at: updated_request.updated_at.into(),
        })
    }

    // 管理员处理续费请求
    pub async fn process_renewal_request(
        &self,
        request_id: String,
        admin_id: String,
        process_request: crate::models::ProcessRenewalRequestRequest,
    ) -> Result<RenewalRequestResponse, AuthError> {
        use crate::prisma::{renewal_request, RenewalRequestStatus};

        let renewal_request = self
            .db
            .renewal_request()
            .find_unique(renewal_request::id::equals(request_id.clone()))
            .include(renewal_request::include!({ instance }))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("续费请求不存在".to_string()))?;

        // 只能处理pending状态的请求
        if renewal_request.status != RenewalRequestStatus::Pending {
            return Err(AuthError::BadRequest(
                "只能处理待处理状态的续费请求".to_string(),
            ));
        }

        let instance = &renewal_request.instance;

        // 确定续费周期（天数）
        let renewal_duration_days = process_request
            .renewal_duration
            .unwrap_or(renewal_request.requested_duration);

        // 生成续费码
        let renewal_code = self
            .db
            .renewal_code()
            .create(
                renewal_duration_days,
                vec![renewal_code::amount::set(Some(
                    renewal_request.payment_amount.clone(),
                ))],
            )
            .exec()
            .await?;

        // 应用续费码到实例
        let current_expiry = instance.license_expires_at;
        let new_expiry = if current_expiry > Utc::now().fixed_offset() {
            // 如果license还没过期，从当前过期时间延长
            current_expiry + Duration::days(renewal_duration_days as i64)
        } else {
            // 如果已过期，从现在开始计算
            Utc::now().fixed_offset() + Duration::days(renewal_duration_days as i64)
        };

        // 更新实例的过期时间
        self.db
            .controller_instance()
            .update(
                controller_instance::id::equals(instance.id.clone()),
                vec![controller_instance::license_expires_at::set(new_expiry)],
            )
            .exec()
            .await?;

        // 标记续费码为已使用
        self.db
            .renewal_code()
            .update(
                renewal_code::id::equals(renewal_code.id.clone()),
                vec![
                    renewal_code::used_at::set(Some(Utc::now().into())),
                    renewal_code::used_by_instance_id::set(Some(instance.id.clone())),
                ],
            )
            .exec()
            .await?;

        // 更新续费请求状态为已处理
        let updated_request = self
            .db
            .renewal_request()
            .update(
                renewal_request::id::equals(request_id),
                vec![
                    renewal_request::status::set(RenewalRequestStatus::Processed),
                    renewal_request::processed_at::set(Some(Utc::now().into())),
                    renewal_request::processed_by::set(Some(admin_id)),
                    renewal_request::admin_notes::set(process_request.admin_notes),
                    renewal_request::generated_renewal_code_id::set(Some(renewal_code.id)),
                    renewal_request::can_reply::set(false),
                ],
            )
            .include(renewal_request::include!({ instance }))
            .exec()
            .await?;

        Ok(RenewalRequestResponse {
            id: updated_request.id,
            payment_method: updated_request.payment_method.to_string().to_lowercase(),
            payment_amount: updated_request.payment_amount,
            payment_proof: updated_request.payment_proof,
            customer_message: updated_request.customer_message,
            status: updated_request.status.to_string(),
            requested_duration: updated_request.requested_duration,
            instance_id: updated_request.instance_id,
            instance_name: Some(updated_request.instance.name),
            processed_at: updated_request
                .processed_at
                .map(|dt| dt.with_timezone(&Utc)),
            processed_by: updated_request.processed_by,
            admin_notes: updated_request.admin_notes,
            generated_renewal_code_id: updated_request.generated_renewal_code_id,
            has_customer_replies: updated_request.has_customer_replies, // TODO: Use actual value from database
            last_message_at: updated_request
                .last_message_at
                .map(|dt| dt.with_timezone(&Utc)),
            can_reply: updated_request.can_reply,
            messages: None,
            created_at: updated_request.created_at.into(),
            updated_at: updated_request.updated_at.into(),
        })
    }

    // 新增的消息相关方法

    // 发送续费请求消息
    pub async fn send_renewal_request_message(
        &self,
        request_id: String,
        instance_id: String,
        message_request: SendMessageRequest,
        author_id: Option<String>,
        author_name: Option<String>,
    ) -> Result<MessageResponse, AuthError> {
        use crate::prisma::{renewal_request, renewal_request_message, MessageType};

        // 验证续费请求存在且属于该实例
        let renewal_request = self
            .db
            .renewal_request()
            .find_unique(renewal_request::id::equals(request_id.clone()))
            .exec()
            .await?
            .ok_or_else(|| AuthError::NotFound("Renewal request not found".to_string()))?;

        if !instance_id.is_empty() && renewal_request.instance_id != instance_id {
            return Err(AuthError::BadRequest(
                "Request does not belong to this instance".to_string(),
            ));
        }

        // 检查是否允许回复
        if !renewal_request.can_reply {
            return Err(AuthError::BadRequest(
                "Reply is not allowed for this request".to_string(),
            ));
        }

        // 转换消息类型
        let message_type = match message_request.message_type.as_str() {
            "customer" => MessageType::Customer,
            "admin" => MessageType::Admin,
            _ => return Err(AuthError::BadRequest("Invalid message type".to_string())),
        };

        let now = Utc::now();

        // 在事务中创建消息并更新续费请求
        let (message, _): (
            crate::prisma::renewal_request_message::Data,
            crate::prisma::renewal_request::Data,
        ) = self
            .db
            ._transaction()
            .run(|tx| async move {
                // 创建消息
                let message = tx
                    .renewal_request_message()
                    .create(
                        renewal_request::id::equals(request_id.clone()),
                        message_type.clone(),
                        message_request.content.clone(),
                        vec![
                            renewal_request_message::author_id::set(author_id.clone()),
                            renewal_request_message::author_name::set(author_name.clone()),
                        ],
                    )
                    .exec()
                    .await?;

                // 更新续费请求的相关字段
                let mut update_params =
                    vec![renewal_request::last_message_at::set(Some(now.into()))];

                // 如果是客户消息，标记有客户回复
                if message_request.message_type == "customer" {
                    update_params.push(renewal_request::has_customer_replies::set(true));
                }

                let updated_request = tx
                    .renewal_request()
                    .update(renewal_request::id::equals(request_id), update_params)
                    .exec()
                    .await?;

                Ok((message, updated_request))
            })
            .await
            .map_err(|e: QueryError| {
                AuthError::Internal(anyhow::anyhow!("Failed to send message: {}", e))
            })?;

        Ok(MessageResponse {
            id: message.id,
            renewal_request_id: message.renewal_request_id,
            message_type: message.message_type.to_string().to_uppercase(),
            content: message.content,
            author_id: message.author_id,
            author_name: message.author_name,
            created_at: message.created_at.into(),
        })
    }

    // 获取续费请求的消息历史
    pub async fn get_renewal_request_messages(
        &self,
        request_id: String,
        instance_id: String,
    ) -> Result<Vec<MessageResponse>, AuthError> {
        use crate::prisma::{renewal_request, renewal_request_message};

        // 验证续费请求存在且属于该实例
        let renewal_request = self
            .db
            .renewal_request()
            .find_unique(renewal_request::id::equals(request_id.clone()))
            .exec()
            .await?
            .ok_or_else(|| AuthError::NotFound("Renewal request not found".to_string()))?;

        if !instance_id.is_empty() && renewal_request.instance_id != instance_id {
            return Err(AuthError::BadRequest(
                "Request does not belong to this instance".to_string(),
            ));
        }

        // 获取消息历史
        let messages = self
            .db
            .renewal_request_message()
            .find_many(vec![renewal_request_message::renewal_request_id::equals(
                request_id,
            )])
            .order_by(renewal_request_message::created_at::order(
                prisma_client_rust::Direction::Asc,
            ))
            .exec()
            .await?;

        Ok(messages
            .into_iter()
            .map(|msg| MessageResponse {
                id: msg.id,
                renewal_request_id: msg.renewal_request_id,
                message_type: msg.message_type.to_string().to_uppercase(),
                content: msg.content,
                author_id: msg.author_id,
                author_name: msg.author_name,
                created_at: msg.created_at.into(),
            })
            .collect())
    }

    // 管理员关闭续费请求（不执行延期）
    pub async fn close_renewal_request(
        &self,
        request_id: String,
        admin_id: String,
        close_request: AdminCloseRequestRequest,
    ) -> Result<RenewalRequestResponse, AuthError> {
        use crate::prisma::{renewal_request, RenewalRequestStatus};

        let renewal_request = self
            .db
            .renewal_request()
            .find_unique(renewal_request::id::equals(request_id.clone()))
            .include(renewal_request::include!({ instance }))
            .exec()
            .await?
            .ok_or_else(|| AuthError::NotFound("Renewal request not found".to_string()))?;

        if renewal_request.status != RenewalRequestStatus::Pending {
            return Err(AuthError::BadRequest(
                "Only pending requests can be closed".to_string(),
            ));
        }

        let updated_request = self
            .db
            .renewal_request()
            .update(
                renewal_request::id::equals(request_id),
                vec![
                    renewal_request::status::set(RenewalRequestStatus::ClosedByAdmin),
                    renewal_request::processed_at::set(Some(Utc::now().into())),
                    renewal_request::processed_by::set(Some(admin_id)),
                    renewal_request::admin_notes::set(close_request.admin_notes),
                    renewal_request::can_reply::set(false), // 关闭后不允许回复
                ],
            )
            .include(renewal_request::include!({ instance }))
            .exec()
            .await?;

        Ok(RenewalRequestResponse {
            id: updated_request.id,
            payment_method: updated_request.payment_method.to_string().to_lowercase(),
            payment_amount: updated_request.payment_amount,
            payment_proof: updated_request.payment_proof,
            customer_message: updated_request.customer_message,
            status: updated_request.status.to_string(),
            requested_duration: updated_request.requested_duration,
            instance_id: updated_request.instance_id,
            instance_name: Some(updated_request.instance.name),
            processed_at: updated_request.processed_at.map(|dt| dt.into()),
            processed_by: updated_request.processed_by,
            admin_notes: updated_request.admin_notes,
            generated_renewal_code_id: updated_request.generated_renewal_code_id,
            has_customer_replies: false, // TODO: Use actual value from database
            last_message_at: updated_request
                .last_message_at
                .map(|dt| dt.with_timezone(&Utc)),
            can_reply: false, // Set to false after admin close
            messages: None,
            created_at: updated_request.created_at.into(),
            updated_at: updated_request.updated_at.into(),
        })
    }
}
