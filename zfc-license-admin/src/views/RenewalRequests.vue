<template>
  <div class="renewal-requests-page">
    <div class="page-header">
      <div class="header-content">
        <h2>Renewal Requests Management</h2>
        <p>Process and manage customer renewal requests</p>
      </div>
      <el-button type="primary" @click="refreshData" :loading="isLoading" :icon="Refresh">
        Refresh
      </el-button>
    </div>
    
    <div class="filters-section">
      <div class="filter-group">
        <el-radio-group v-model="statusFilter" @change="handleFilterChange">
          <el-radio-button label="">All</el-radio-button>
          <el-radio-button :label="RenewalRequestStatus.PENDING">Pending</el-radio-button>
          <el-radio-button :label="RenewalRequestStatus.PROCESSED">Processed</el-radio-button>
          <el-radio-button :label="RenewalRequestStatus.CANCELLED">Cancelled</el-radio-button>
          <el-radio-button :label="RenewalRequestStatus.CLOSED_BY_ADMIN">Closed by Admin</el-radio-button>
        </el-radio-group>
      </div>
      <div class="filter-group">
        <el-input
          v-model="instanceFilter"
          placeholder="Filter by instance ID..."
          clearable
          @change="handleFilterChange"
          style="width: 200px"
        />
      </div>
      <div class="filter-group">
        <el-checkbox
          v-model="hasCustomerRepliesFilter"
          @change="handleFilterChange"
        >
          Show requests with customer replies
        </el-checkbox>
      </div>
    </div>
    
    <div class="table-container">
      <el-table
        :data="renewalRequests"
        v-loading="isLoading"
        style="width: 100%"
        stripe
        :default-sort="{ prop: 'createdAt', order: 'descending' }"
      >
        <el-table-column prop="id" label="Request ID" min-width="100">
          <template #default="{ row }">
            <code class="request-id">{{ row.id.substring(0, 8) }}...</code>
          </template>
        </el-table-column>
        
        <el-table-column prop="instanceName" label="Instance" min-width="120">
          <template #default="{ row }">
            <div class="instance-info">
              <div class="instance-name">{{ row.instanceName || 'Unknown' }}</div>
              <div class="instance-id">{{ row.instanceId.substring(0, 8) }}...</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="requestedDuration" label="Duration" min-width="100" sortable>
          <template #default="{ row }">
            <el-tag type="info">{{ row.requestedDuration }}个月</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="paymentAmount" label="Amount" min-width="100" sortable>
          <template #default="{ row }">
            <span class="price-text">¥{{ row.paymentAmount }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="paymentMethod" label="Payment Method" min-width="140">
          <template #default="{ row }">
            <el-tag :type="row.paymentMethod === 'alipay_hongbao' ? 'warning' : 'info'">
              {{ row.paymentMethod === 'alipay_hongbao' ? '支付宝口令红包' : '加密货币' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="Status" min-width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 'PENDING' ? 'warning' : 
                     row.status === 'PROCESSED' ? 'success' : 'danger'"
            >
              {{ row.status === 'PENDING' ? '待处理' : 
                 row.status === 'PROCESSED' ? '已处理' : '已取消' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="Created At" min-width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="Actions" min-width="180" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="showRequestDetail(row)"
              :icon="View"
            >
              View Details
            </el-button>
            <el-button
              v-if="row.status === 'PENDING'"
              type="success"
              size="small"
              @click="showProcessDialog(row)"
              :icon="Check"
            >
              Process
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- Request Detail Dialog -->
    <el-dialog
      v-model="showDetailDialog"
      title="Renewal Request Details"
      width="900px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedRequest" class="request-detail">
        <!-- Request Information -->
        <el-descriptions :column="2" border class="mb-4">
          <el-descriptions-item label="Request ID">
            <code>{{ selectedRequest.id }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="Status">
            <el-tag 
              :type="selectedRequest.status === 'PENDING' ? 'warning' : 
                     selectedRequest.status === 'PROCESSED' ? 'success' : 
                     selectedRequest.status === 'CLOSED_BY_ADMIN' ? 'info' : 'danger'"
            >
              {{ getStatusText(selectedRequest.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Instance">
            {{ selectedRequest.instanceName || 'Unknown' }}
          </el-descriptions-item>
          <el-descriptions-item label="Instance ID">
            <code>{{ selectedRequest.instanceId }}</code>
          </el-descriptions-item>
          <el-descriptions-item label="Requested Duration">
            {{ selectedRequest.requestedDuration }}个月
          </el-descriptions-item>
          <el-descriptions-item label="Payment Amount">
            ¥{{ selectedRequest.paymentAmount }}
          </el-descriptions-item>
          <el-descriptions-item label="Payment Method">
            {{ selectedRequest.paymentMethod === 'alipay_hongbao' ? '支付宝口令红包' : '加密货币' }}
          </el-descriptions-item>
          <el-descriptions-item label="Created At">
            {{ formatDate(selectedRequest.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="Payment Proof" span="2">
            <pre class="payment-proof">{{ selectedRequest.paymentProof }}</pre>
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedRequest.customerMessage" label="Customer Message" span="2">
            <div class="customer-message">{{ selectedRequest.customerMessage }}</div>
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedRequest.processedAt" label="Processed At" span="2">
            {{ formatDate(selectedRequest.processedAt) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedRequest.generatedRenewalCodeId" label="Applied Renewal Code" span="2">
            <code>{{ selectedRequest.generatedRenewalCodeId }}</code>
          </el-descriptions-item>
        </el-descriptions>

        <!-- Conversation History -->
        <div class="conversation-section">
          <h4>Conversation History</h4>
          
          <div class="conversation-container" v-loading="loadingMessages">
            <div v-if="requestMessages.length === 0" class="no-messages">
              <el-empty 
                description="No messages yet" 
                :image-size="60"
              />
            </div>
            
            <div v-else class="messages-list">
              <div 
                v-for="message in (Array.isArray(requestMessages) ? requestMessages : [])" 
                :key="message.id"
                :class="['message-item', { 'customer-message': message.messageType === 'CUSTOMER', 'admin-message': message.messageType === 'ADMIN' }]"
              >
                <div class="message-header">
                  <span class="message-sender">
                    {{ message.messageType === 'CUSTOMER' ? 'Customer' : 'Admin' }}
                  </span>
                  <span class="message-time">{{ formatDate(message.createdAt) }}</span>
                </div>
                <div class="message-content">{{ message.content }}</div>
              </div>
            </div>
          </div>

          <!-- Admin Reply Section (only if request is still pending) -->
          <div v-if="selectedRequest.status === 'PENDING'" class="admin-reply-section">
            <el-form>
              <el-form-item label="Send Reply">
                <el-input
                  v-model="adminReplyMessage"
                  type="textarea"
                  :rows="3"
                  placeholder="Enter your reply to the customer..."
                  maxlength="500"
                  show-word-limit
                  :disabled="sendingMessage"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  @click="handleSendAdminMessage"
                  :loading="sendingMessage"
                  :disabled="!adminReplyMessage.trim()"
                >
                  Send Reply
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <el-alert
            v-else
            title="Request Closed"
            description="This request has been processed or closed. No further messages can be sent."
            type="info"
            show-icon
            :closable="false"
          />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">Close</el-button>
        <el-button
          v-if="selectedRequest && selectedRequest.status === 'PENDING'"
          type="success"
          @click="showProcessDialog(selectedRequest); showDetailDialog = false"
        >
          Process Request
        </el-button>
        <el-button
          v-if="selectedRequest && selectedRequest.status === 'PENDING'"
          type="warning"
          @click="showCloseRequestDialog"
        >
          Close Request
        </el-button>
      </template>
    </el-dialog>

    <!-- Close Request Dialog -->
    <el-dialog
      v-model="showCloseDialog"
      title="Close Renewal Request"
      width="500px"
    >
      <div v-if="selectedRequest">
        <el-alert
          title="Close Request"
          :description="`Are you sure you want to close this renewal request? Request ID: ${selectedRequest.id}`"
          type="warning"
          show-icon
          :closable="false"
          class="mb-4"
        />
        
        <el-form>
          <el-form-item label="Admin Notes (Optional)">
            <el-input
              v-model="closeRequestNotes"
              type="textarea"
              :rows="3"
              placeholder="Optional notes about why this request is being closed..."
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="showCloseDialog = false">Cancel</el-button>
        <el-button
          type="warning"
          @click="handleCloseRequest"
          :loading="closingRequest"
        >
          Close Request
        </el-button>
      </template>
    </el-dialog>
    
    <!-- Process Request Dialog -->
    <el-dialog
      v-model="showProcessRequestDialog"
      title="Process Renewal Request"
      width="600px"
    >
      <div v-if="processingRequest">
        <el-alert
          title="Processing Renewal Request"
          :description="`Instance: ${processingRequest.instanceName} | Amount: ¥${processingRequest.paymentAmount} | Duration: ${processingRequest.requestedDuration}个月`"
          type="info"
          show-icon
          :closable="false"
          class="mb-4"
        />
        
        <el-form
          ref="processFormRef"
          :model="processForm"
          :rules="processFormRules"
          label-width="140px"
        >
          <el-form-item label="Renewal Duration" prop="renewalDuration" required>
            <el-input-number
              v-model="processForm.renewalDuration"
              :min="1"
              :max="3650"
              :step="1"
              style="width: 200px"
            />
            <span class="ml-2">days (recommended: {{ processingRequest.requestedDuration * 30 }})</span>
            <div class="form-hint">
              This will extend the license by the specified number of days from current expiry date
            </div>
          </el-form-item>

          <el-form-item label="Admin Notes" prop="adminNotes">
            <el-input
              v-model="processForm.adminNotes"
              type="textarea"
              :rows="3"
              placeholder="Optional notes about this renewal request processing..."
              maxlength="500"
              show-word-limit
            />
            <div class="form-hint">
              These notes will be stored for audit purposes
            </div>
          </el-form-item>
        </el-form>
        
        <el-alert
          title="Processing will automatically:"
          description="1. Generate a new renewal code with the specified duration
2. Apply the renewal code to extend the instance license
3. Mark this request as processed
4. All changes are irreversible once confirmed"
          type="warning"
          show-icon
          :closable="false"
        />
      </div>
      
      <template #footer>
        <el-button @click="showProcessRequestDialog = false">Cancel</el-button>
        <el-button
          type="success"
          @click="handleProcessRequest"
          :loading="isProcessing"
          :disabled="!processForm.renewalDuration"
        >
          Process & Apply Renewal
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Refresh, View, Check } from '@element-plus/icons-vue'
import { renewalRequestsApi } from '@/api'
import type { RenewalRequest, ProcessRenewalRequestData } from '@/types'
import { RenewalRequestStatus } from '@/types'

// State
const isLoading = ref(false)
const isProcessing = ref(false)
const renewalRequests = ref<RenewalRequest[]>([])
const selectedRequest = ref<RenewalRequest | null>(null)
const processingRequest = ref<RenewalRequest | null>(null)
const showDetailDialog = ref(false)
const showProcessRequestDialog = ref(false)
const processFormRef = ref<FormInstance | null>(null)

// 消息相关状态
const requestMessages = ref<any[]>([])
const loadingMessages = ref(false)
const sendingMessage = ref(false)
const adminReplyMessage = ref('')
const showCloseDialog = ref(false)
const closingRequest = ref(false)
const closeRequestNotes = ref('')

// Filters
const statusFilter = ref<'' | RenewalRequestStatus>('')
const instanceFilter = ref('')
const hasCustomerRepliesFilter = ref(false)

// Pagination
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// Process form
const processForm = reactive({
  renewalDuration: 30,
  adminNotes: ''
})

// Process form validation rules
const processFormRules = {
  renewalDuration: [
    { required: true, message: 'Please enter renewal duration', trigger: 'blur' },
    { type: 'number', min: 1, max: 3650, message: 'Duration must be between 1 and 3650 days', trigger: 'blur' }
  ],
  adminNotes: [
    { max: 500, message: 'Admin notes cannot exceed 500 characters', trigger: 'blur' }
  ]
}

// Methods
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const refreshData = async () => {
  await loadRenewalRequests()
}

const loadRenewalRequests = async () => {
  isLoading.value = true
  try {
    const params: any = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    if (statusFilter.value) {
      params.status = statusFilter.value
    }
    
    if (instanceFilter.value) {
      params.instanceId = instanceFilter.value
    }

    if (hasCustomerRepliesFilter.value) {
      params.hasCustomerReplies = true
    }

    const response = await renewalRequestsApi.getRenewalRequests(params)
    const apiData = response.data
    const pageData = apiData?.data
    
    // Ensure we always assign an array to prevent map/reduce errors
    renewalRequests.value = Array.isArray(pageData?.data) ? pageData.data : []
    pagination.total = typeof pageData?.total === 'number' ? pageData.total : 0
    
  } catch (error: any) {
    console.error('loadRenewalRequests error:', error)
    renewalRequests.value = []
    pagination.total = 0
    ElMessage.error(error.message || 'Failed to load renewal requests')
  } finally {
    isLoading.value = false
  }
}

const handleFilterChange = () => {
  pagination.page = 1
  loadRenewalRequests()
}

const handleSizeChange = (newSize: number) => {
  pagination.pageSize = newSize
  pagination.page = 1
  loadRenewalRequests()
}

const handleCurrentChange = (newPage: number) => {
  pagination.page = newPage
  loadRenewalRequests()
}

const showRequestDetail = async (request: RenewalRequest) => {
  selectedRequest.value = request
  showDetailDialog.value = true
  adminReplyMessage.value = ''
  requestMessages.value = []
  
  // 加载消息历史
  await loadRequestMessages(request.id)
}

// 消息相关方法
const loadRequestMessages = async (requestId: string) => {
  loadingMessages.value = true
  try {
    const response = await renewalRequestsApi.getMessages(requestId)
    // Ensure we always assign an array to prevent map/reduce errors
    requestMessages.value = Array.isArray(response.data) ? response.data : []
  } catch (error: any) {
    console.error('Failed to load messages:', error)
    requestMessages.value = []
  } finally {
    loadingMessages.value = false
  }
}

const handleSendAdminMessage = async () => {
  if (!adminReplyMessage.value.trim() || !selectedRequest.value) return
  
  sendingMessage.value = true
  try {
    await renewalRequestsApi.sendMessage(selectedRequest.value.id, adminReplyMessage.value.trim())
    ElMessage.success('Message sent successfully')
    adminReplyMessage.value = ''
    
    // 重新加载消息历史
    await loadRequestMessages(selectedRequest.value.id)
  } catch (error: any) {
    ElMessage.error(error.message || 'Failed to send message')
  } finally {
    sendingMessage.value = false
  }
}

const showCloseRequestDialog = () => {
  closeRequestNotes.value = ''
  showCloseDialog.value = true
}

const handleCloseRequest = async () => {
  if (!selectedRequest.value) return
  
  closingRequest.value = true
  try {
    await renewalRequestsApi.closeRequest(selectedRequest.value.id, closeRequestNotes.value)
    ElMessage.success('Request closed successfully')
    showCloseDialog.value = false
    showDetailDialog.value = false
    
    // 刷新数据
    await loadRenewalRequests()
  } catch (error: any) {
    ElMessage.error(error.message || 'Failed to close request')
  } finally {
    closingRequest.value = false
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'PENDING':
      return '待处理'
    case 'PROCESSED':
      return '已处理'
    case 'CANCELLED':
      return '已取消'
    case 'CLOSED_BY_ADMIN':
      return '管理员关闭'
    default:
      return status
  }
}

const resetProcessForm = () => {
  processForm.renewalDuration = 30
  processForm.adminNotes = ''

  if (processFormRef.value) {
    processFormRef.value.resetFields()
  }
}

const showProcessDialog = (request: RenewalRequest) => {
  processingRequest.value = request
  processForm.renewalDuration = request.requestedDuration * 30 // Convert months to days
  processForm.adminNotes = ''
  showProcessRequestDialog.value = true
}

const handleProcessRequest = async () => {
  if (!processingRequest.value || !processFormRef.value) return

  const currentRequest = processingRequest.value
  const valid = await processFormRef.value.validate().catch(() => false)
  if (!valid) {
    ElMessage.warning('Please check the form fields')
    return
  }

  try {
    await ElMessageBox.confirm(
      `Are you sure you want to process this renewal request? This will extend the license by ${processForm.renewalDuration} days and cannot be undone.`,
      'Confirm Processing',
      {
        confirmButtonText: 'Process Request',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }
    )

    isProcessing.value = true

    const processData: ProcessRenewalRequestData = {
      renewalDuration: processForm.renewalDuration,
      adminNotes: processForm.adminNotes || undefined
    }

    await renewalRequestsApi.processRenewalRequest(
      currentRequest.id,
      processData
    )

    ElMessage.success('Renewal request processed successfully')
    showProcessRequestDialog.value = false
    processingRequest.value = null

    // Reset form
    resetProcessForm()

    // Refresh the data to show updated status
    await loadRenewalRequests()

  } catch (error: any) {
    if (error === 'cancel') return
    ElMessage.error(error.message || 'Failed to process renewal request')
  } finally {
    isProcessing.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadRenewalRequests()
})
</script>

<style scoped>
.renewal-requests-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filters-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-top: 1px solid #ebeef5;
}

.request-id {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.instance-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.instance-name {
  font-weight: 500;
  font-size: 13px;
}

.instance-id {
  font-size: 11px;
  color: #909399;
  font-family: 'Courier New', monospace;
}

.price-text {
  font-weight: 500;
  color: #e6a23c;
}

.request-detail {
  margin-bottom: 20px;
}

.payment-proof {
  background: #f9f9f9;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 150px;
  overflow-y: auto;
  margin: 0;
}

.customer-message {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  padding: 8px;
  font-size: 13px;
  line-height: 1.5;
}

/* Conversation Styles */
.conversation-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.conversation-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.conversation-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.no-messages {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid transparent;
}

.message-item.customer-message {
  background: #e7f3ff;
  border-color: #b3d8ff;
  margin-left: 0;
  margin-right: 20px;
}

.message-item.admin-message {
  background: #f0f9ff;
  border-color: #bfdbfe;
  margin-left: 20px;
  margin-right: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
}

.message-sender {
  font-weight: 600;
  color: #409eff;
}

.message-time {
  color: #909399;
}

.message-content {
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-word;
}

.admin-reply-section {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-2 {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .renewal-requests-page {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .filters-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  .pagination-container {
    padding: 12px;
  }
}
</style>